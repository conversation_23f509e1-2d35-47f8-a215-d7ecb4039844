<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="PBIppt.vsto" version="*******" publicKeyToken="d3af86de3c7fb742" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="PBIppt" asmv2:product="PBIppt" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.7.2" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="PBIppt.dll.manifest" size="15081">
      <assemblyIdentity name="PBIppt.dll" version="*******" publicKeyToken="d3af86de3c7fb742" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>7aeNW2YRX56DNlHF24Ps7IRUB/u0wMreuft6XOlNI1w=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=EVE\071716" issuerKeyHash="3151950d1a3f63514e5c3ad29f1869a719b42855" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>LzfJJ/lchbGMuwEdTl2vnHcCZ4ZdIfsSlpjXwvo7d6I=</DigestValue></Reference></SignedInfo><SignatureValue>CW8tTrxjDOi5Cf6ZdAVna5QndZuYx+bV7GMS5sKMBhPR/b9RLXMwK2NH2Tkc1FaPvi7DbHke2MaSrJy9QIbBmXbkQav9hzRz/ynnnBy49KlqDwhMxc2xcCev2koYoofshU8N3kCMF/QAurqvbae6/JZij4BnDCTzmT52WhHS2Lo=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>7OStg4ejnwgQFmDFixqWE4TV0jLfDP2Cwe4VFHb53QFCNcJmKmFEtdh1W20Y/NmVOWd/fhDQjPkDuu9XhdanmUCVBga0+d1Mn0kjWqmsf9estHhNYZD8U6dPiQuXtKCy/aDuG0EeSZJNK1hfMZlyQBOMY4oXwOUoMbKzz2PWaKU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="a2773bfac2d7989612fb215d866702779caf5d4e1d01bb8cb1855cf927c9372f" Description="" Url=""><as:assemblyIdentity name="PBIppt.vsto" version="*******" publicKeyToken="d3af86de3c7fb742" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=EVE\071716</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>UmvUllmxukWi6Eu9GLAgRUZ2zlcueLbbTiMZRWVy9nU=</DigestValue></Reference></SignedInfo><SignatureValue>6B3+65ab4b61S2CBhLdU/MkTZZxYAw2+Fzx/kuTM++VGPooNS3GExmgMSB/rSHipAM6lx/DcalwFFzedujuSGcKGNJ3KyKyBbSLSgkIf0pnBnbfuegyhO4LEVVtK+zVGanO7hrG7EMrM7tnjy01sS2zWWXdouqTmRG9aKtwOkm0=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>7OStg4ejnwgQFmDFixqWE4TV0jLfDP2Cwe4VFHb53QFCNcJmKmFEtdh1W20Y/NmVOWd/fhDQjPkDuu9XhdanmUCVBga0+d1Mn0kjWqmsf9estHhNYZD8U6dPiQuXtKCy/aDuG0EeSZJNK1hfMZlyQBOMY4oXwOUoMbKzz2PWaKU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIBwTCCASqgAwIBAgIQLZs7+UUk/ZlNyc59aBwysDANBgkqhkiG9w0BAQsFADAfMR0wGwYDVQQDHhQARQBWAEUAXAAwADcAMQA3ADEANjAeFw0yNTA2MTkwODIzMDJaFw0yNjA2MTkxNDIzMDJaMB8xHTAbBgNVBAMeFABFAFYARQBcADAANwAxADcAMQA2MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDs5K2Dh6OfCBAWYMWLGpYThNXSMt8M/YLB7hUUdvndAUI1wmYqYUS12HVbbRj82ZU5Z39+ENCM+QO671eF1qeZQJUGBrT53UyfSSNaqax/16y0eE1hkPxTp0+JC5e0oLL9oO4bQR5Jkk0rWF8xmXJAE4xjihfA5SgxsrPPY9ZopQIDAQABMA0GCSqGSIb3DQEBCwUAA4GBAEgTbXl96lf8pdN7EIxDF3AWI1FphAidTPCJ1GAQLfWSaf7rfRWX7na/r5oNQ+Cjc/VZFmtKES95SFYI02dcFfbkINRuBnd8lDbqGYBN5KW2TAFxJfWQ1sKRBWHBvxa6gyS35ztbYTrngHjfJHqdHHGCDzcBXgRTQk/+qcDm1jil</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>