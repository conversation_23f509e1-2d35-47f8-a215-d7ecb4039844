using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Office = Microsoft.Office.Core;

namespace PBIppt.ImageProcessing
{
    /// <summary>
    /// PowerPoint内置裁剪功能助手
    /// 直接调用PowerPoint的裁剪API，用户可以继续手动调整
    /// </summary>
    public static class PowerPointCropHelper
    {
        /// <summary>
        /// 确保图片Scale为标准值 - 关键修复
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        private static void EnsureStandardScale(PowerPoint.Shape shape)
        {
            try
            {
                // 解锁宽高比，允许独立调整宽度和高度
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;

                // 重置Scale为100%标准值
                shape.ScaleHeight(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
                shape.ScaleWidth(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                // Scale标准化失败时继续执行，不影响主要功能
            }
        }



        /// <summary>
        /// 根据电芯检测结果直接设置PowerPoint图片的裁剪属性（指定图片尺寸，默认使用直接转换）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <param name="actualPixelWidth">实际图片像素宽度</param>
        /// <param name="actualPixelHeight">实际图片像素高度</param>
        public static void ApplyDetectionResultToCrop(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult,
            int actualPixelWidth, int actualPixelHeight)
        {
            // 默认使用直接转换方法
            ApplyDetectionResultToCrop(shape, detectionResult, actualPixelWidth, actualPixelHeight, true);
        }

        /// <summary>
        /// 根据电芯检测结果设置PowerPoint图片的裁剪属性（指定图片尺寸和转换方法）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <param name="actualPixelWidth">实际图片像素宽度</param>
        /// <param name="actualPixelHeight">实际图片像素高度</param>
        /// <param name="useDirectConversion">是否使用直接转换方法（true=直接按原始尺寸，false=考虑PPT显示缩放）</param>
        public static void ApplyDetectionResultToCrop(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult,
            int actualPixelWidth, int actualPixelHeight, bool useDirectConversion)
        {
            try
            {
                // 验证形状类型
                if (shape.Type != Office.MsoShapeType.msoPicture)
                {
                    throw new ArgumentException("选中的形状不是图片");
                }

                // 关键修复：确保Scale为标准值
                EnsureStandardScale(shape);
                var currentWidth = shape.Width;
                var currentHeight = shape.Height;

                Debug.WriteLine("currentWidth" + currentWidth + "currentHeight" + currentHeight);
                // 只使用百分比坐标转换
                CropCoordinates cropCoords = null;
                bool hasValidPercentageCoords = HasValidPercentageCoordinates(detectionResult);

                if (hasValidPercentageCoords)
                {
                    cropCoords = ConvertPercentageCoordsToPptCrop(
                        detectionResult.CropLeftPercent,
                        detectionResult.CropTopPercent,
                        detectionResult.CropRightPercent,
                        detectionResult.CropBottomPercent,
                        currentWidth,
                        currentHeight);
                }
                else
                {
                    // 如果没有有效的百分比坐标，抛出异常
                    throw new ArgumentException("没有有效的百分比坐标，无法进行裁剪");
                }



                // 应用裁剪到PowerPoint图片
                if (cropCoords != null)
                {
                    // 🔍 首先分析图片尺寸和可能的旋转问题
                    Debug.WriteLine("=== 图片尺寸和旋转分析 ===");
                    string dimensionAnalysis = AnalyzeImageDimensionsAndRotation(shape, detectionResult);
                    Debug.WriteLine(dimensionAnalysis);



                    // 方式5：考虑缩放修正的裁剪
                    Debug.WriteLine("方式5：考虑缩放修正的裁剪");
                    try
                    {
                        TestCropMethod5_ScaleCorrected(shape, detectionResult);
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"方式5调用失败: {ex.Message}");
                    }
                }

                // 裁剪完成后自动调整图片尺寸为2.1cm高度
                AdjustImageSizeTo21cm(shape);

                // 选中图片展示结果
                SelectShapeToShowResult(shape);
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new Exception($"应用检测结果失败: {ex.Message}", ex);
            }
        }

        // 已删除 ConvertApiCoordsToPptCrop_Direct 方法，只保留百分比坐标转换



        /// <summary>
        /// 检查API返回的百分比坐标是否有效
        /// </summary>
        /// <param name="detectionResult">检测结果</param>
        /// <returns>如果百分比坐标有效返回true，否则返回false</returns>
        private static bool HasValidPercentageCoordinates(BatteryDetectionApiResult detectionResult)
        {
            // 检查百分比坐标是否都大于0（API返回0表示无效或未提供）
            bool hasValidCoords = detectionResult.CropLeftPercent > 0 ||
                                 detectionResult.CropTopPercent > 0 ||
                                 detectionResult.CropRightPercent > 0 ||
                                 detectionResult.CropBottomPercent > 0;

            return hasValidCoords;
        }

        /// <summary>
        /// 将API返回的百分比坐标转换为PowerPoint裁剪坐标
        /// </summary>
        /// <param name="leftPercent">左边裁剪百分比</param>
        /// <param name="topPercent">上边裁剪百分比</param>
        /// <param name="rightPercent">右边裁剪百分比</param>
        /// <param name="bottomPercent">下边裁剪百分比</param>
        /// <param name="shapeWidth">PowerPoint形状宽度</param>
        /// <param name="shapeHeight">PowerPoint形状高度</param>
        /// <returns>PowerPoint裁剪坐标</returns>
        private static CropCoordinates ConvertPercentageCoordsToPptCrop(
            float leftPercent, float topPercent, float rightPercent, float bottomPercent,
            float shapeWidth, float shapeHeight)
        {
            // 直接将百分比转换为PowerPoint的裁剪points
            // API返回的百分比表示从各边裁剪的比例
            var cropCoords = new CropCoordinates
            {
                Left = leftPercent * shapeWidth,
                Top = topPercent * shapeHeight,
                Right = rightPercent * shapeWidth,
                Bottom = bottomPercent * shapeHeight
            };

            return cropCoords;
        }



        /// <summary>
        /// 直接使用百分比设置PowerPoint Shape的裁剪属性
        /// 避免任何像素到points的转换，直接基于图片的百分比位置进行裁剪
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        private static void ApplyPercentageCropToShape(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始百分比裁剪 ===");
                Debug.WriteLine($"输入百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

                // 获取图片的原始尺寸（PowerPoint中的显示尺寸）
                float shapeWidth = shape.Width;
                float shapeHeight = shape.Height;
                Debug.WriteLine($"PowerPoint形状尺寸: {shapeWidth:F2}x{shapeHeight:F2} points");

                // 直接将百分比转换为PowerPoint的裁剪points
                float cropLeft = leftPercent * shapeWidth;
                float cropTop = topPercent * shapeHeight;
                float cropRight = rightPercent * shapeWidth;
                float cropBottom = bottomPercent * shapeHeight;

                Debug.WriteLine($"计算的裁剪points: Left={cropLeft:F2}, Top={cropTop:F2}, Right={cropRight:F2}, Bottom={cropBottom:F2}");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 百分比裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"百分比裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"百分比裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 直接使用API返回的百分比坐标进行裁剪（适用于被拉伸的图片）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        private static void ApplyApiPercentageCropToShape(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始API百分比坐标裁剪 ===");
                Debug.WriteLine($"API百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

                // 重新分析：基于左边图片成功，右边图片失败的情况
                // API百分比坐标应该是边界框位置，需要转换为裁剪距离
                Debug.WriteLine("使用边界框位置转换为裁剪距离的逻辑");

                float cropLeft = leftPercent * 100;              // 从左边裁剪的百分比 = 边界框左边位置
                float cropTop = topPercent * 100;                // 从上边裁剪的百分比 = 边界框上边位置
                float cropRight = (1 - rightPercent) * 100;   // 从右边裁剪的百分比 = 100% - 边界框右边位置
                float cropBottom = (1 - bottomPercent) * 100; // 从下边裁剪的百分比 = 100% - 边界框下边位置

                // 验证保留区域大小
                float preservedWidthPercent = (rightPercent - leftPercent) * 100;
                float preservedHeightPercent = (bottomPercent - topPercent) * 100;
                Debug.WriteLine($"保留区域百分比: 宽度={preservedWidthPercent:F2}%, 高度={preservedHeightPercent:F2}%");

                Debug.WriteLine($"转换为PowerPoint裁剪百分比: Left={cropLeft:F2}%, Top={cropTop:F2}%, Right={cropRight:F2}%, Bottom={cropBottom:F2}%");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ API百分比坐标裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"API百分比坐标裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"API百分比坐标裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 使用百分比坐标进行裁剪，基于原始图片尺寸（适用于被拉伸的图片）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        /// <param name="originalPixelWidth">原始图片像素宽度</param>
        /// <param name="originalPixelHeight">原始图片像素高度</param>
        private static void ApplyPercentageCropToShapeWithOriginalSize(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent, int originalPixelWidth, int originalPixelHeight)
        {
            try
            {
                Debug.WriteLine("=== 开始基于原始尺寸的百分比裁剪 ===");
                Debug.WriteLine($"输入百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");
                Debug.WriteLine($"原始图片像素尺寸: {originalPixelWidth}x{originalPixelHeight}");

                // 根据Microsoft官方文档：裁剪是相对于图片的原始尺寸计算的
                // API百分比坐标是位置，需要转换为PowerPoint的裁剪距离
                float cropLeft = leftPercent * originalPixelWidth;  // 从左边裁剪的距离
                float cropTop = topPercent * originalPixelHeight;   // 从上边裁剪的距离
                float cropRight = (1.0f - rightPercent) * originalPixelWidth;   // 从右边裁剪的距离
                float cropBottom = (1.0f - bottomPercent) * originalPixelHeight; // 从下边裁剪的距离

                Debug.WriteLine($"基于原始尺寸计算的裁剪points: Left={cropLeft:F2}, Top={cropTop:F2}, Right={cropRight:F2}, Bottom={cropBottom:F2}");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 基于原始尺寸的百分比裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"基于原始尺寸的百分比裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"基于原始尺寸的百分比裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 通过像素和百分比计算裁剪的point值，然后应用到PowerPoint形状
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPixels">左边裁剪像素值</param>
        /// <param name="topPixels">上边裁剪像素值</param>
        /// <param name="rightPixels">右边裁剪像素值</param>
        /// <param name="bottomPixels">下边裁剪像素值</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        /// <summary>
        /// 增强版裁剪方法：通过多种策略确保裁剪准确性
        /// </summary>
        public static void ApplyCropToShapeByPoint(PowerPoint.Shape shape,
            float leftPixels, float topPixels, float rightPixels, float bottomPixels,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始增强版裁剪计算 ===");

                // 🔍 添加坐标理解测试
                Debug.WriteLine("--- 坐标理解分析 ---");
                Debug.WriteLine($"API像素坐标: Left={leftPixels}, Top={topPixels}, Right={rightPixels}, Bottom={bottomPixels}");
                Debug.WriteLine($"API百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

                // 确保图片Scale为标准值
                EnsureStandardScale(shape);

                // 获取当前PowerPoint形状尺寸（points）
                float currentWidthPoints = shape.Width;
                float currentHeightPoints = shape.Height;
                Debug.WriteLine($"当前形状尺寸: {currentWidthPoints:F2}x{currentHeightPoints:F2} points");

                // 策略1：直接使用百分比坐标（最可靠）
                var strategy1Result = CalculateCropByPercentage(leftPercent, topPercent, rightPercent, bottomPercent,
                    currentWidthPoints, currentHeightPoints);

                // 策略2：通过像素坐标计算（用于验证）
                var strategy2Result = CalculateCropByPixels(leftPixels, topPixels, rightPixels, bottomPixels,
                    shape, currentWidthPoints, currentHeightPoints);

                // 策略3：混合策略（当两种方法差异较大时使用）
                var finalCropValues = SelectBestCropStrategy(strategy1Result, strategy2Result,
                    leftPercent, topPercent, rightPercent, bottomPercent);

                // 应用裁剪
                ApplyCropValues(shape, finalCropValues);

                // 验证和调试输出
                VerifyAndLogCropResults(shape, finalCropValues, leftPercent, topPercent, rightPercent, bottomPercent,
                    currentWidthPoints, currentHeightPoints);

                // 安全的界面刷新
                RefreshPowerPointInterface(shape);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"增强版裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"增强版裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 策略1：基于百分比计算裁剪值（修正版）
        /// API返回的百分比直接表示从各边裁剪的比例
        /// </summary>
        private static CropValues CalculateCropByPercentage(float leftPercent, float topPercent,
            float rightPercent, float bottomPercent, float widthPoints, float heightPoints)
        {
            // 🔥 修正：API返回的百分比直接就是裁剪比例，不需要转换
            var result = new CropValues
            {
                Left = leftPercent * widthPoints,      // 从左边裁剪的距离
                Top = topPercent * heightPoints,       // 从上边裁剪的距离
                Right = rightPercent * widthPoints,    // 从右边裁剪的距离
                Bottom = bottomPercent * heightPoints, // 从下边裁剪的距离
                Strategy = "百分比计算(修正版)"
            };

            Debug.WriteLine($"策略1(百分比修正): Left={result.Left:F2}, Top={result.Top:F2}, Right={result.Right:F2}, Bottom={result.Bottom:F2}");

            // 验证保留区域
            float preservedWidth = widthPoints - result.Left - result.Right;
            float preservedHeight = heightPoints - result.Top - result.Bottom;
            Debug.WriteLine($"保留区域: {preservedWidth:F2}x{preservedHeight:F2} points");
            Debug.WriteLine($"保留比例: 宽度={preservedWidth / widthPoints * 100:F1}%, 高度={preservedHeight / heightPoints * 100:F1}%");

            return result;
        }

        /// <summary>
        /// 策略2：基于像素坐标计算裁剪值（修正版）
        /// API返回的是边界框坐标，需要转换为裁剪距离
        /// </summary>
        private static CropValues CalculateCropByPixels(float leftPixels, float topPixels,
            float rightPixels, float bottomPixels, PowerPoint.Shape shape, float widthPoints, float heightPoints)
        {
            try
            {
                // 获取图片的原始尺寸（像素）
                var pixelDimensions = GetImagePixelDimensions(shape);
                int originalPixelWidth = pixelDimensions.width;
                int originalPixelHeight = pixelDimensions.height;
                Debug.WriteLine($"图片原始像素尺寸: {originalPixelWidth}x{originalPixelHeight}");

                // 🔥 修正：API返回的直接就是裁剪距离（像素）
                // leftPixels = 从左边裁剪掉的像素数
                // topPixels = 从上边裁剪掉的像素数
                // rightPixels = 从右边裁剪掉的像素数
                // bottomPixels = 从下边裁剪掉的像素数

                Debug.WriteLine($"API返回的裁剪像素距离: Left={leftPixels}, Top={topPixels}, Right={rightPixels}, Bottom={bottomPixels}");

                // 验证保留区域大小
                float preservedPixelWidth = originalPixelWidth - leftPixels - rightPixels;
                float preservedPixelHeight = originalPixelHeight - topPixels - bottomPixels;
                Debug.WriteLine($"保留的像素区域: {preservedPixelWidth:F2}x{preservedPixelHeight:F2}");

                if (preservedPixelWidth <= 0 || preservedPixelHeight <= 0)
                {
                    Debug.WriteLine($"⚠️ 像素裁剪计算异常: 保留区域为 {preservedPixelWidth:F2}x{preservedPixelHeight:F2}");
                    return new CropValues { Strategy = "像素计算(异常)", IsValid = false };
                }

                // 计算像素到points的转换比例
                float pixelsToPointsX = widthPoints / originalPixelWidth;
                float pixelsToPointsY = heightPoints / originalPixelHeight;
                Debug.WriteLine($"像素到points转换比例: X={pixelsToPointsX:F4}, Y={pixelsToPointsY:F4}");

                var result = new CropValues
                {
                    Left = leftPixels * pixelsToPointsX,    // 直接转换裁剪距离
                    Top = topPixels * pixelsToPointsY,      // 直接转换裁剪距离
                    Right = rightPixels * pixelsToPointsX,  // 直接转换裁剪距离
                    Bottom = bottomPixels * pixelsToPointsY, // 直接转换裁剪距离
                    Strategy = "像素计算(修正版)"
                };

                Debug.WriteLine($"策略2(像素修正): Left={result.Left:F2}, Top={result.Top:F2}, Right={result.Right:F2}, Bottom={result.Bottom:F2}");

                // 验证转换后的保留区域
                float preservedPointsWidth = widthPoints - result.Left - result.Right;
                float preservedPointsHeight = heightPoints - result.Top - result.Bottom;
                Debug.WriteLine($"转换后保留区域: {preservedPointsWidth:F2}x{preservedPointsHeight:F2} points");

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"像素计算策略失败: {ex.Message}");
                // 返回一个无效的结果，让选择算法忽略它
                return new CropValues { Strategy = "像素计算(失败)", IsValid = false };
            }
        }

        /// <summary>
        /// 直接设置PowerPoint Shape的裁剪属性
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="cropCoords">裁剪坐标</param>
        private static void ApplyCropToShape(PowerPoint.Shape shape, CropCoordinates cropCoords)
        {
            try
            {
                Debug.WriteLine("开始应用裁剪到PowerPoint形状");

                shape.PictureFormat.CropLeft = cropCoords.Left;
                shape.PictureFormat.CropTop = cropCoords.Top;
                shape.PictureFormat.CropRight = cropCoords.Right;
                shape.PictureFormat.CropBottom = cropCoords.Bottom;

                // 安全的界面刷新方式 - 优化版本，避免不必要的异常
                try
                {
                    var app = shape.Application;
                    if (app?.ActiveWindow != null)
                    {
                        // 直接使用PowerPoint支持的刷新方法，避免dynamic调用产生异常
                        try
                        {
                            // 使用GotoSlide方法强制刷新（PowerPoint原生支持）
                            var activeWindow = app.ActiveWindow;
                            if (activeWindow?.View?.Slide != null)
                            {
                                var currentSlideIndex = activeWindow.View.Slide.SlideIndex;
                                activeWindow.View.GotoSlide(currentSlideIndex);
                                Debug.WriteLine("已使用GotoSlide方法刷新PowerPoint界面");
                            }
                            else
                            {
                                Debug.WriteLine("无法获取当前幻灯片，跳过界面刷新");
                            }
                        }
                        catch (Exception refreshEx)
                        {
                            Debug.WriteLine($"PowerPoint界面刷新失败: {refreshEx.Message}");
                            // 界面刷新失败不影响核心功能，继续执行
                        }
                    }
                    else
                    {
                        Debug.WriteLine("无法获取ActiveWindow，跳过界面刷新");
                    }
                }
                catch (Exception refreshEx)
                {
                    Debug.WriteLine($"刷新界面失败: {refreshEx.Message}，继续执行");
                    // 不抛出异常，继续执行
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用裁剪到形状失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"应用裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调整图片尺寸为2.1cm高度（保持宽高比）并确保图片在幻灯片内
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        public static void AdjustImageSizeTo21cm(PowerPoint.Shape shape)
        {
            try
            {
                Debug.WriteLine("=== 开始调整图片尺寸为2.1cm高度 ===");

                // 目标高度：2.1cm = 59.535点
                float targetHeightCm = 2.1f;
                float targetHeightPoints = targetHeightCm * 28.35f; // 1cm = 28.35点

                Debug.WriteLine($"目标高度: {targetHeightCm}cm = {targetHeightPoints:F2}点");

                // 获取当前图片尺寸和位置
                float currentWidth = shape.Width;
                float currentHeight = shape.Height;
                float currentLeft = shape.Left;
                float currentTop = shape.Top;
                float currentAspectRatio = currentWidth / currentHeight;

                // 计算新的宽度（保持宽高比）
                float newWidth = targetHeightPoints * currentAspectRatio;
                float newHeight = targetHeightPoints;

                // 获取幻灯片尺寸
                var slide = shape.Parent as PowerPoint.Slide;
                if (slide != null)
                {
                    float slideWidth = slide.Master.Width;
                    float slideHeight = slide.Master.Height;
                    Debug.WriteLine($"幻灯片尺寸: {slideWidth:F2}x{slideHeight:F2}点");
                    float currentCenterX = currentLeft + currentWidth / 2;
                    float currentCenterY = currentTop + currentHeight / 2;

                    // 计算新的左上角位置（保持中心点不变）
                    float newLeft = currentCenterX - newWidth / 2;
                    float newTop = currentCenterY - newHeight / 2;

                    // 确保图片不超出幻灯片边界
                    if (newLeft < 0)
                    {
                        newLeft = 10; // 左边距10点
                        Debug.WriteLine("调整Left位置，避免超出左边界");
                    }
                    if (newTop < 0)
                    {
                        newTop = 10; // 上边距10点
                        Debug.WriteLine("调整Top位置，避免超出上边界");
                    }
                    if (newLeft + newWidth > slideWidth)
                    {
                        newLeft = slideWidth - newWidth - 10; // 右边距10点
                        Debug.WriteLine("调整Left位置，避免超出右边界");
                    }
                    if (newTop + newHeight > slideHeight)
                    {
                        newTop = slideHeight - newHeight - 10; // 下边距10点
                        Debug.WriteLine("调整Top位置，避免超出下边界");
                    }

                    Debug.WriteLine($"调整后位置: Left={newLeft:F2}, Top={newTop:F2}");

                    // 解锁宽高比，允许独立调整
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;

                    // 设置新的尺寸和位置
                    shape.Width = newWidth;
                    shape.Height = newHeight;
                    shape.Left = newLeft;
                    shape.Top = newTop;

                    Debug.WriteLine($"✅ 图片尺寸和位置已调整: 尺寸{newWidth:F2}x{newHeight:F2}点, 位置({newLeft:F2},{newTop:F2})");
                    Debug.WriteLine($"实际高度: {newHeight / 28.35f:F2}cm");
                }
                else
                {
                    Debug.WriteLine("⚠️ 无法获取幻灯片信息，仅调整尺寸");

                    // 解锁宽高比，允许独立调整
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;

                    // 设置新的尺寸
                    shape.Width = newWidth;
                    shape.Height = newHeight;

                    Debug.WriteLine($"✅ 图片尺寸已调整为2.1cm高度: {newWidth:F2}x{newHeight:F2}点");
                    Debug.WriteLine($"实际高度: {newHeight / 28.35f:F2}cm");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"调整图片尺寸失败: {ex.Message}");
                Logger.Exception(ex);
                // 不抛出异常，避免影响主要功能
            }
        }

        /// <summary>
        /// 选中图片以展示裁剪结果，不进入裁剪模式
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        public static void SelectShapeToShowResult(PowerPoint.Shape shape)
        {
            try
            {
                try
                {
                    var app = shape.Application;
                    var activeWindow = app.ActiveWindow;
                    var selection = activeWindow.Selection;
                    selection.Unselect();
                    System.Threading.Thread.Sleep(50); // 短暂延迟确保取消选择生效
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"取消选择失败: {ex.Message}");
                }

                // 选中图片以展示裁剪结果
                shape.Select();
                Debug.WriteLine("已选中图片，展示裁剪结果");

                // 强制刷新PowerPoint界面以确保裁剪效果可见
                try
                {
                    var app = shape.Application;
                    var activeWindow = app.ActiveWindow;
                    var view = activeWindow.View;
                    view.Zoom = view.Zoom; // 触发重绘
                    Debug.WriteLine("已触发PowerPoint界面刷新");
                }
                catch (Exception refreshEx)
                {
                    Debug.WriteLine($"刷新界面失败: {refreshEx.Message}");
                }

            }
            catch (Exception ex)
            {
                Debug.WriteLine($"选中图片展示结果失败: {ex.Message}");
                Logger.Exception(ex);
                // 这个错误不是致命的，不抛出异常
            }
        }




        /// <summary>
        /// 策略3：选择最佳的裁剪策略
        /// </summary>
        private static CropValues SelectBestCropStrategy(CropValues strategy1, CropValues strategy2,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            // 如果策略2无效，直接使用策略1
            if (!strategy2.IsValid)
            {
                Debug.WriteLine("策略选择: 使用策略1(百分比)，策略2无效");
                return ValidateAndCorrectCropValues(strategy1, leftPercent, topPercent, rightPercent, bottomPercent);
            }

            // 计算两种策略的差异
            float leftDiff = Math.Abs(strategy1.Left - strategy2.Left);
            float topDiff = Math.Abs(strategy1.Top - strategy2.Top);
            float rightDiff = Math.Abs(strategy1.Right - strategy2.Right);
            float bottomDiff = Math.Abs(strategy1.Bottom - strategy2.Bottom);

            float maxDiff = Math.Max(Math.Max(leftDiff, topDiff), Math.Max(rightDiff, bottomDiff));
            Debug.WriteLine($"策略差异分析: 最大差异={maxDiff:F2} points");

            // 如果差异较小（小于5个points），优先使用百分比策略
            if (maxDiff < 5.0f)
            {
                Debug.WriteLine("策略选择: 使用策略1(百分比)，差异较小");
                return ValidateAndCorrectCropValues(strategy1, leftPercent, topPercent, rightPercent, bottomPercent);
            }

            // 如果差异较大，进行更详细的分析
            // 🔥 修正：检查百分比坐标的合理性
            // API返回的百分比坐标表示从各边裁剪的比例
            bool percentCoordsValid = leftPercent >= 0 && leftPercent <= 1 &&
                                    topPercent >= 0 && topPercent <= 1 &&
                                    rightPercent >= 0 && rightPercent <= 1 &&
                                    bottomPercent >= 0 && bottomPercent <= 1;

            // 额外检查：确保裁剪后还有内容保留
            float remainingWidthPercent = 1.0f - leftPercent - rightPercent;   // 修正：直接减去两边的裁剪比例
            float remainingHeightPercent = 1.0f - topPercent - bottomPercent;  // 修正：直接减去上下的裁剪比例
            bool hasRemainingContent = remainingWidthPercent > 0.01f && remainingHeightPercent > 0.01f;

            percentCoordsValid = percentCoordsValid && hasRemainingContent;

            Debug.WriteLine($"百分比坐标验证: 基本范围={leftPercent >= 0 && leftPercent <= 1 && topPercent >= 0 && topPercent <= 1 && rightPercent >= 0 && rightPercent <= 1 && bottomPercent >= 0 && bottomPercent <= 1}");
            Debug.WriteLine($"剩余内容检查: 宽度={remainingWidthPercent:F3}, 高度={remainingHeightPercent:F3}, 有效={hasRemainingContent}");

            if (percentCoordsValid)
            {
                Debug.WriteLine("策略选择: 使用策略1(百分比)，百分比坐标有效");
                return ValidateAndCorrectCropValues(strategy1, leftPercent, topPercent, rightPercent, bottomPercent);
            }
            else
            {
                Debug.WriteLine("策略选择: 使用策略2(像素)，百分比坐标异常");
                return ValidateAndCorrectCropValues(strategy2, leftPercent, topPercent, rightPercent, bottomPercent);
            }
        }

        /// <summary>
        /// 验证和修正裁剪值
        /// </summary>
        private static CropValues ValidateAndCorrectCropValues(CropValues cropValues,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            var correctedValues = new CropValues
            {
                Left = Math.Max(0, cropValues.Left),
                Top = Math.Max(0, cropValues.Top),
                Right = Math.Max(0, cropValues.Right),
                Bottom = Math.Max(0, cropValues.Bottom),
                Strategy = cropValues.Strategy + "(已修正)",
                IsValid = true
            };

            // 检查是否进行了修正
            bool wasCorrected = correctedValues.Left != cropValues.Left ||
                              correctedValues.Top != cropValues.Top ||
                              correctedValues.Right != cropValues.Right ||
                              correctedValues.Bottom != cropValues.Bottom;

            if (wasCorrected)
            {
                Debug.WriteLine($"⚠️ 裁剪值已修正: 原值({cropValues.Left:F2},{cropValues.Top:F2},{cropValues.Right:F2},{cropValues.Bottom:F2}) " +
                              $"-> 修正值({correctedValues.Left:F2},{correctedValues.Top:F2},{correctedValues.Right:F2},{correctedValues.Bottom:F2})");
                Logger.Warning($"裁剪坐标存在负值，已自动修正为0");
            }

            return correctedValues;
        }

        /// <summary>
        /// 应用裁剪值到形状
        /// </summary>
        private static void ApplyCropValues(PowerPoint.Shape shape, CropValues cropValues)
        {
            try
            {
                // 确保图片不锁定宽高比
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                Debug.WriteLine("✓ 已解锁图片宽高比");

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropValues.Left;
                shape.PictureFormat.CropTop = cropValues.Top;
                shape.PictureFormat.CropRight = cropValues.Right;
                shape.PictureFormat.CropBottom = cropValues.Bottom;

                Debug.WriteLine($"✓ 裁剪已应用 - 策略: {cropValues.Strategy}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用裁剪失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 验证和记录裁剪结果
        /// </summary>
        private static void VerifyAndLogCropResults(PowerPoint.Shape shape, CropValues appliedCrop,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent,
            float originalWidth, float originalHeight)
        {
            try
            {
                // 获取裁剪后的尺寸
                float finalWidth = shape.Width;
                float finalHeight = shape.Height;

                // 计算实际裁剪区域
                float actualCropWidth = originalWidth - appliedCrop.Left - appliedCrop.Right;
                float actualCropHeight = originalHeight - appliedCrop.Top - appliedCrop.Bottom;

                // 计算预期裁剪区域
                float expectedCropWidth = (rightPercent - leftPercent) * originalWidth;
                float expectedCropHeight = (bottomPercent - topPercent) * originalHeight;

                // 计算误差
                float widthError = Math.Abs(actualCropWidth - expectedCropWidth);
                float heightError = Math.Abs(actualCropHeight - expectedCropHeight);
                float errorPercentage = Math.Max(widthError / expectedCropWidth, heightError / expectedCropHeight) * 100;

                Debug.WriteLine("=== 裁剪验证结果 ===");
                Debug.WriteLine($"裁剪前尺寸: {originalWidth:F2}x{originalHeight:F2} points");
                Debug.WriteLine($"裁剪后尺寸: {finalWidth:F2}x{finalHeight:F2} points");
                Debug.WriteLine($"实际裁剪区域: {actualCropWidth:F2}x{actualCropHeight:F2} points");
                Debug.WriteLine($"预期裁剪区域: {expectedCropWidth:F2}x{expectedCropHeight:F2} points");
                Debug.WriteLine($"裁剪误差: 宽度={widthError:F2}, 高度={heightError:F2}, 误差率={errorPercentage:F1}%");

                // 如果误差超过5%，记录警告
                if (errorPercentage > 5.0f)
                {
                    Logger.Warning($"裁剪误差较大: {errorPercentage:F1}%, 可能需要调整算法");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证裁剪结果失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 简化的测试方法：仅使用百分比坐标进行裁剪
        /// </summary>
        public static void ApplyCropByPercentageOnly(PowerPoint.Shape shape,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 仅使用百分比坐标裁剪 ===");

                // 确保图片Scale为标准值
                EnsureStandardScale(shape);

                // 获取当前PowerPoint形状尺寸（points）
                float currentWidthPoints = shape.Width;
                float currentHeightPoints = shape.Height;
                Debug.WriteLine($"当前形状尺寸: {currentWidthPoints:F2}x{currentHeightPoints:F2} points");

                // 🔥 修正：直接使用百分比坐标计算裁剪值
                // API返回的百分比直接表示从各边裁剪的比例
                float cropLeft = leftPercent * currentWidthPoints;    // 从左边裁剪
                float cropTop = topPercent * currentHeightPoints;     // 从上边裁剪
                float cropRight = rightPercent * currentWidthPoints;  // 从右边裁剪
                float cropBottom = bottomPercent * currentHeightPoints; // 从下边裁剪

                Debug.WriteLine($"计算的裁剪值: Left={cropLeft:F2}, Top={cropTop:F2}, Right={cropRight:F2}, Bottom={cropBottom:F2}");

                // 验证计算结果
                float finalWidth = currentWidthPoints - cropLeft - cropRight;
                float finalHeight = currentHeightPoints - cropTop - cropBottom;
                Debug.WriteLine($"预期裁剪后尺寸: {finalWidth:F2}x{finalHeight:F2} points");

                if (finalWidth <= 0 || finalHeight <= 0)
                {
                    throw new ArgumentException($"裁剪计算错误：裁剪后尺寸为 {finalWidth:F2}x{finalHeight:F2}");
                }

                // 确保图片不锁定宽高比
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                Debug.WriteLine("✓ 已解锁图片宽高比");

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 百分比裁剪已应用");

                // 验证实际结果
                float actualFinalWidth = shape.Width;
                float actualFinalHeight = shape.Height;
                Debug.WriteLine($"实际裁剪后尺寸: {actualFinalWidth:F2}x{actualFinalHeight:F2} points");

                // 计算误差
                float widthError = Math.Abs(actualFinalWidth - finalWidth);
                float heightError = Math.Abs(actualFinalHeight - finalHeight);
                Debug.WriteLine($"尺寸误差: 宽度={widthError:F2}, 高度={heightError:F2}");

                // 刷新界面
                RefreshPowerPointInterface(shape);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"百分比裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"百分比裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 安全刷新PowerPoint界面
        /// </summary>
        private static void RefreshPowerPointInterface(PowerPoint.Shape shape)
        {
            try
            {
                var app = shape.Application;
                if (app?.ActiveWindow != null)
                {
                    var activeWindow = app.ActiveWindow;
                    if (activeWindow?.View?.Slide != null)
                    {
                        var currentSlideIndex = activeWindow.View.Slide.SlideIndex;
                        activeWindow.View.GotoSlide(currentSlideIndex);
                        Debug.WriteLine("✓ 已刷新PowerPoint界面");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"界面刷新失败: {ex.Message}");
                // 不抛出异常，避免影响主要功能
            }
        }

        /// <summary>
        /// 获取图片的原始像素尺寸
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>图片的像素宽度和高度</returns>
        private static (int width, int height) GetImagePixelDimensions(PowerPoint.Shape shape)
        {
            try
            {


                // 方法1：尝试使用ImageExtractor提取图片
                try
                {
                    var image = ImageExtractor.ExtractImageFromShape(shape);
                    if (image != null)
                    {
                        int width = image.Width;
                        int height = image.Height;

                        Debug.WriteLine($"通过ImageExtractor获取到图片原始像素尺寸: {width}x{height}");

                        // 释放图片资源
                        image.Dispose();

                        return (width, height);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"ImageExtractor方法失败: {ex.Message}");
                }

                // 方法2：尝试通过PowerPoint的PictureFormat属性获取
                try
                {
                    // 获取图片的原始尺寸（以points为单位）
                    var originalWidth = shape.PictureFormat.CropLeft + shape.Width + shape.PictureFormat.CropRight;
                    var originalHeight = shape.PictureFormat.CropTop + shape.Height + shape.PictureFormat.CropBottom;

                    Debug.WriteLine($"通过PictureFormat计算的原始尺寸: {originalWidth:F2}x{originalHeight:F2} points");

                    // 假设72 DPI (1 point = 1 pixel at 72 DPI)
                    // 这是一个近似值，但比完全错误的默认值要好
                    int pixelWidth = (int)Math.Round(originalWidth);
                    int pixelHeight = (int)Math.Round(originalHeight);

                    Debug.WriteLine($"转换为像素尺寸: {pixelWidth}x{pixelHeight}");

                    if (pixelWidth > 0 && pixelHeight > 0)
                    {
                        return (pixelWidth, pixelHeight);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"PictureFormat方法失败: {ex.Message}");
                }

                // 方法3：基于当前显示尺寸进行估算
                try
                {
                    // 假设图片在PowerPoint中没有被大幅缩放
                    // 使用当前显示尺寸作为近似的像素尺寸
                    int estimatedWidth = (int)Math.Round(shape.Width);
                    int estimatedHeight = (int)Math.Round(shape.Height);

                    Debug.WriteLine($"基于显示尺寸估算的像素尺寸: {estimatedWidth}x{estimatedHeight}");

                    if (estimatedWidth > 0 && estimatedHeight > 0)
                    {
                        return (estimatedWidth, estimatedHeight);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"尺寸估算失败: {ex.Message}");
                }

                // 最后的备选方案：使用合理的默认尺寸
                Debug.WriteLine("所有方法都失败，使用默认图片尺寸: 1920x1080");
                return (1920, 1080);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取图片像素尺寸完全失败: {ex.Message}");
                Logger.Exception(ex);

                // 发生错误时使用默认尺寸
                Debug.WriteLine("使用默认图片尺寸: 1920x1080");
                return (1920, 1080);
            }
        }

        /// <summary>
        /// 诊断裁剪问题的综合工具
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">API检测结果</param>
        /// <returns>诊断报告</returns>
        public static string DiagnoseCropIssues(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult)
        {
            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== 裁剪问题诊断报告 ===");

                // 1. 基本信息
                report.AppendLine($"形状类型: {shape.Type}");
                report.AppendLine($"当前尺寸: {shape.Width:F2}x{shape.Height:F2} points");

                // 2. API返回的坐标信息
                report.AppendLine("\n--- API返回坐标 ---");
                report.AppendLine($"像素坐标: Left={detectionResult.CropLeft}, Top={detectionResult.CropTop}, Right={detectionResult.CropRight}, Bottom={detectionResult.CropBottom}");
                report.AppendLine($"百分比坐标: Left={detectionResult.CropLeftPercent:F6}, Top={detectionResult.CropTopPercent:F6}, Right={detectionResult.CropRightPercent:F6}, Bottom={detectionResult.CropBottomPercent:F6}");
                report.AppendLine($"图片信息: {detectionResult.ImageWidth}x{detectionResult.ImageHeight} pixels");

                // 3. 图片尺寸获取测试
                report.AppendLine("\n--- 图片尺寸获取测试 ---");
                var pixelDimensions = GetImagePixelDimensions(shape);
                int pixelWidth = pixelDimensions.width;
                int pixelHeight = pixelDimensions.height;
                report.AppendLine($"获取到的像素尺寸: {pixelWidth}x{pixelHeight}");
                report.AppendLine($"API报告的像素尺寸: {detectionResult.ImageWidth}x{detectionResult.ImageHeight}");

                bool sizeMismatch = Math.Abs(pixelWidth - detectionResult.ImageWidth) > 10 ||
                                  Math.Abs(pixelHeight - detectionResult.ImageHeight) > 10;
                if (sizeMismatch)
                {
                    report.AppendLine("⚠️ 警告: 获取的图片尺寸与API报告不一致！");
                }

                // 4. 坐标合理性检查
                report.AppendLine("\n--- 坐标合理性检查 ---");
                bool percentValid = detectionResult.CropLeftPercent >= 0 && detectionResult.CropLeftPercent <= 1 &&
                                  detectionResult.CropTopPercent >= 0 && detectionResult.CropTopPercent <= 1 &&
                                  detectionResult.CropRightPercent >= 0 && detectionResult.CropRightPercent <= 1 &&
                                  detectionResult.CropBottomPercent >= 0 && detectionResult.CropBottomPercent <= 1 &&
                                  detectionResult.CropLeftPercent < detectionResult.CropRightPercent &&
                                  detectionResult.CropTopPercent < detectionResult.CropBottomPercent;

                report.AppendLine($"百分比坐标有效性: {(percentValid ? "✓ 有效" : "✗ 无效")}");

                if (!percentValid)
                {
                    report.AppendLine("⚠️ 百分比坐标存在问题，可能导致裁剪不准确");
                }

                // 5. 当前裁剪状态
                report.AppendLine("\n--- 当前裁剪状态 ---");
                var currentCrop = $"Left={shape.PictureFormat.CropLeft:F2}, Top={shape.PictureFormat.CropTop:F2}, Right={shape.PictureFormat.CropRight:F2}, Bottom={shape.PictureFormat.CropBottom:F2}";
                report.AppendLine($"当前裁剪值: {currentCrop}");

                bool hasCropping = shape.PictureFormat.CropLeft > 0 || shape.PictureFormat.CropTop > 0 ||
                                 shape.PictureFormat.CropRight > 0 || shape.PictureFormat.CropBottom > 0;
                report.AppendLine($"是否已裁剪: {(hasCropping ? "是" : "否")}");

                // 6. 建议
                report.AppendLine("\n--- 建议 ---");
                if (sizeMismatch)
                {
                    report.AppendLine("• 图片尺寸不匹配可能是由于PowerPoint内部缩放导致，建议使用百分比坐标");
                }
                if (!percentValid)
                {
                    report.AppendLine("• 百分比坐标异常，建议检查API返回数据或使用像素坐标作为备选");
                }
                if (percentValid && sizeMismatch)
                {
                    report.AppendLine("• 推荐使用增强版裁剪方法 ApplyCropToShapeByPoint");
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                return $"诊断过程中发生错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 验证裁剪是否已应用
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>裁剪状态信息</returns>
        public static string VerifyCropApplication(PowerPoint.Shape shape)
        {
            try
            {
                if (shape.Type != Office.MsoShapeType.msoPicture)
                {
                    return "选中的形状不是图片";
                }

                var cropLeft = shape.PictureFormat.CropLeft;
                var cropTop = shape.PictureFormat.CropTop;
                var cropRight = shape.PictureFormat.CropRight;
                var cropBottom = shape.PictureFormat.CropBottom;

                var status = $"当前裁剪属性 - Left: {cropLeft:F2}, Top: {cropTop:F2}, Right: {cropRight:F2}, Bottom: {cropBottom:F2}";
                Debug.WriteLine(status);

                bool hasCropping = cropLeft > 0 || cropTop > 0 || cropRight > 0 || cropBottom > 0;
                if (hasCropping)
                {
                    return $"✓ 裁剪已应用 - {status}";
                }
                else
                {
                    return $"✗ 未检测到裁剪 - {status}";
                }
            }
            catch (Exception ex)
            {
                var error = $"验证裁剪状态失败: {ex.Message}";
                Debug.WriteLine(error);
                Logger.Exception(ex);
                return error;
            }
        }



        #region 测试方法



     

        /// <summary>
        /// 分析图片尺寸和旋转问题
        /// </summary>
        public static string AnalyzeImageDimensionsAndRotation(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult)
        {
            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== 图片尺寸和旋转分析 ===");

                // 1. PowerPoint形状信息
                float shapeWidth = shape.Width;
                float shapeHeight = shape.Height;
                report.AppendLine($"PowerPoint形状尺寸: {shapeWidth:F2} x {shapeHeight:F2} points");

                // 2. API返回的图片信息
                int apiWidth = detectionResult.ImageWidth;
                int apiHeight = detectionResult.ImageHeight;
                float[] apiDpi = detectionResult.ImageDpi;
                report.AppendLine($"API返回图片尺寸: {apiWidth} x {apiHeight} pixels");
                report.AppendLine($"API返回DPI: {apiDpi[0]} x {apiDpi[1]}");

                // 3. 通过ImageExtractor获取的尺寸
                var pixelDimensions = GetImagePixelDimensions(shape);
                int extractedWidth = pixelDimensions.width;
                int extractedHeight = pixelDimensions.height;
                report.AppendLine($"ImageExtractor获取尺寸: {extractedWidth} x {extractedHeight} pixels");

                // 4. 计算理论转换
                float theoreticalWidthPoints = apiWidth / apiDpi[0] * 72;
                float theoreticalHeightPoints = apiHeight / apiDpi[1] * 72;
                report.AppendLine($"理论Points尺寸: {theoreticalWidthPoints:F2} x {theoreticalHeightPoints:F2} points");

                // 5. 检查是否可能有旋转
                report.AppendLine("\n--- 旋转检查 ---");

                // 检查是否宽高互换了
                bool possibleRotation90 = Math.Abs(shapeWidth - theoreticalHeightPoints) < 10 &&
                                         Math.Abs(shapeHeight - theoreticalWidthPoints) < 10;

                if (possibleRotation90)
                {
                    report.AppendLine("⚠️ 可能存在90°旋转：形状尺寸与理论尺寸的宽高互换匹配");
                }

                // 6. 计算缩放比例
                float scaleX = shapeWidth / theoreticalWidthPoints;
                float scaleY = shapeHeight / theoreticalHeightPoints;
                report.AppendLine($"缩放比例: X={scaleX:F3}, Y={scaleY:F3}");

                // 7. 分析结果
                bool uniformScaling = Math.Abs(scaleX - scaleY) < 0.01f;
                if (uniformScaling)
                {
                    report.AppendLine("✓ 等比缩放，坐标转换相对简单");
                }
                else
                {
                    report.AppendLine("⚠️ 非等比缩放，需要分别处理X和Y坐标");
                }

                // 8. 建议
                report.AppendLine("\n--- 建议 ---");
                if (possibleRotation90)
                {
                    report.AppendLine("🔄 建议：图片可能被旋转了，应该使用旋转后的坐标系");
                }
                else
                {
                    report.AppendLine("✓ 图片方向正常，无需考虑旋转");
                }

                // 9. 坐标转换建议
                if (possibleRotation90)
                {
                    report.AppendLine("\n旋转坐标转换公式:");
                    report.AppendLine("  rotatedLeft = originalTop");
                    report.AppendLine("  rotatedTop = 1 - originalRight");
                    report.AppendLine("  rotatedRight = 1 - originalBottom");
                    report.AppendLine("  rotatedBottom = originalLeft");

                    float rotatedCropLeft = detectionResult.CropTopPercent * shapeWidth;
                    float rotatedCropTop = (1.0f - detectionResult.CropRightPercent) * shapeHeight;
                    float rotatedCropRight = (1.0f - detectionResult.CropBottomPercent) * shapeWidth;
                    float rotatedCropBottom = detectionResult.CropLeftPercent * shapeHeight;

                    report.AppendLine($"  旋转后裁剪值: Left={rotatedCropLeft:F2}, Top={rotatedCropTop:F2}, Right={rotatedCropRight:F2}, Bottom={rotatedCropBottom:F2}");
                }
                else
                {
                    report.AppendLine("使用正常坐标系，无需旋转调整");
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                return $"尺寸分析失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试方法5：考虑PowerPoint缩放修正的裁剪
        /// </summary>
        private static void TestCropMethod5_ScaleCorrected(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult)
        {
            try
            {
                Debug.WriteLine("--- 方式5：考虑PowerPoint缩放修正的裁剪 ---");

                // 保存原始状态
                var originalCropLeft = shape.PictureFormat.CropLeft;
                var originalCropTop = shape.PictureFormat.CropTop;
                var originalCropRight = shape.PictureFormat.CropRight;
                var originalCropBottom = shape.PictureFormat.CropBottom;

                float currentWidth = shape.Width;
                float currentHeight = shape.Height;

                // 计算PowerPoint的缩放比例
                int apiWidth = detectionResult.ImageWidth;
                int apiHeight = detectionResult.ImageHeight;
                float[] apiDpi = detectionResult.ImageDpi;

                float theoreticalWidthPoints = apiWidth / apiDpi[0] * 72;
                float theoreticalHeightPoints = apiHeight / apiDpi[1] * 72;

                float scaleX = currentWidth / theoreticalWidthPoints;
                float scaleY = currentHeight / theoreticalHeightPoints;

                Debug.WriteLine($"PowerPoint缩放比例: X={scaleX:F3}, Y={scaleY:F3}");

                // 基于理论尺寸计算裁剪，然后应用缩放
                Debug.WriteLine("基于理论尺寸计算裁剪值");
                float theoreticalCropLeft = detectionResult.CropLeftPercent * theoreticalWidthPoints;
                float theoreticalCropTop = detectionResult.CropTopPercent * theoreticalHeightPoints;
                float theoreticalCropRight = detectionResult.CropRightPercent * theoreticalWidthPoints;
                float theoreticalCropBottom = detectionResult.CropBottomPercent * theoreticalHeightPoints;

                Debug.WriteLine($"理论裁剪值: Left={theoreticalCropLeft:F2}, Top={theoreticalCropTop:F2}, Right={theoreticalCropRight:F2}, Bottom={theoreticalCropBottom:F2}");

                // 应用缩放
                float scaledCropLeft = theoreticalCropLeft * scaleX;
                float scaledCropTop = theoreticalCropTop * scaleY;
                float scaledCropRight = theoreticalCropRight * scaleX;
                float scaledCropBottom = theoreticalCropBottom * scaleY;

                Debug.WriteLine($"缩放后裁剪值: Left={scaledCropLeft:F2}, Top={scaledCropTop:F2}, Right={scaledCropRight:F2}, Bottom={scaledCropBottom:F2}");

                // 应用裁剪
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                shape.PictureFormat.CropLeft = scaledCropLeft;
                shape.PictureFormat.CropTop = scaledCropTop;
                shape.PictureFormat.CropRight = scaledCropRight;
                shape.PictureFormat.CropBottom = scaledCropBottom;

                // 验证结果
                float finalWidth = shape.Width;
                float finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后实际尺寸: {finalWidth:F2}x{finalHeight:F2} points");

                // 计算预期尺寸
                float expectedWidth = currentWidth - scaledCropLeft - scaledCropRight;
                float expectedHeight = currentHeight - scaledCropTop - scaledCropBottom;
                Debug.WriteLine($"预期尺寸: {expectedWidth:F2}x{expectedHeight:F2} points");

                float widthError = Math.Abs(finalWidth - expectedWidth);
                float heightError = Math.Abs(finalHeight - expectedHeight);
                Debug.WriteLine($"尺寸误差: 宽度={widthError:F2}, 高度={heightError:F2}");


            }
            catch (Exception ex)
            {
                Debug.WriteLine($"方式5测试失败: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 裁剪坐标结构
    /// </summary>
    public class CropCoordinates
    {
        /// <summary>
        /// 左边裁剪距离（points）
        /// </summary>
        public float Left { get; set; }

        /// <summary>
        /// 上边裁剪距离（points）
        /// </summary>
        public float Top { get; set; }

        /// <summary>
        /// 右边裁剪距离（points）
        /// </summary>
        public float Right { get; set; }

        /// <summary>
        /// 下边裁剪距离（points）
        /// </summary>
        public float Bottom { get; set; }
    }

    /// <summary>
    /// 裁剪值结构（用于增强版裁剪方法）
    /// </summary>
    public class CropValues
    {
        /// <summary>
        /// 左边裁剪距离（points）
        /// </summary>
        public float Left { get; set; }

        /// <summary>
        /// 上边裁剪距离（points）
        /// </summary>
        public float Top { get; set; }

        /// <summary>
        /// 右边裁剪距离（points）
        /// </summary>
        public float Right { get; set; }

        /// <summary>
        /// 下边裁剪距离（points）
        /// </summary>
        public float Bottom { get; set; }

        /// <summary>
        /// 计算策略名称
        /// </summary>
        public string Strategy { get; set; }

        /// <summary>
        /// 是否为有效的裁剪值
        /// </summary>
        public bool IsValid { get; set; } = true;
    }
}
