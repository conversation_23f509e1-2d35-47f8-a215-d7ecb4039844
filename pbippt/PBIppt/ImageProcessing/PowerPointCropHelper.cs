using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Office = Microsoft.Office.Core;

namespace PBIppt.ImageProcessing
{
    /// <summary>
    /// PowerPoint内置裁剪功能助手
    /// 直接调用PowerPoint的裁剪API，用户可以继续手动调整
    /// </summary>
    public static class PowerPointCropHelper
    {
        /// <summary>
        /// 确保图片Scale为标准值 - 关键修复
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        private static void EnsureStandardScale(PowerPoint.Shape shape)
        {
            try
            {
                // 解锁宽高比，允许独立调整宽度和高度
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;

                // 重置Scale为100%标准值
                shape.ScaleHeight(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
                shape.ScaleWidth(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                // Scale标准化失败时继续执行，不影响主要功能
            }
        }



        /// <summary>
        /// 根据电芯检测结果直接设置PowerPoint图片的裁剪属性（指定图片尺寸，默认使用直接转换）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <param name="actualPixelWidth">实际图片像素宽度</param>
        /// <param name="actualPixelHeight">实际图片像素高度</param>
        public static void ApplyDetectionResultToCrop(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult,
            int actualPixelWidth, int actualPixelHeight)
        {
            // 默认使用直接转换方法
            ApplyDetectionResultToCrop(shape, detectionResult, actualPixelWidth, actualPixelHeight, true);
        }

        /// <summary>
        /// 根据电芯检测结果设置PowerPoint图片的裁剪属性（指定图片尺寸和转换方法）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <param name="actualPixelWidth">实际图片像素宽度</param>
        /// <param name="actualPixelHeight">实际图片像素高度</param>
        /// <param name="useDirectConversion">是否使用直接转换方法（true=直接按原始尺寸，false=考虑PPT显示缩放）</param>
        public static void ApplyDetectionResultToCrop(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult,
            int actualPixelWidth, int actualPixelHeight, bool useDirectConversion)
        {
            try
            {
                // 验证形状类型
                if (shape.Type != Office.MsoShapeType.msoPicture)
                {
                    throw new ArgumentException("选中的形状不是图片");
                }

 

                    Debug.WriteLine("方式5：考虑缩放修正的裁剪");
                    try
                    {
                        TestCropMethod5_ScaleCorrected(shape, detectionResult);
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"方式5调用失败: {ex.Message}");
                    }
                

                // 裁剪完成后自动调整图片尺寸为2.1cm高度
                AdjustImageSizeTo21cm(shape);

                // 选中图片展示结果
                SelectShapeToShowResult(shape);
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new Exception($"应用检测结果失败: {ex.Message}", ex);
            }
        }

        // 已删除 ConvertApiCoordsToPptCrop_Direct 方法，只保留百分比坐标转换



        /// <summary>
        /// 检查API返回的百分比坐标是否有效
        /// </summary>
        /// <param name="detectionResult">检测结果</param>
        /// <returns>如果百分比坐标有效返回true，否则返回false</returns>
        private static bool HasValidPercentageCoordinates(BatteryDetectionApiResult detectionResult)
        {
            // 检查百分比坐标是否都大于0（API返回0表示无效或未提供）
            bool hasValidCoords = detectionResult.CropLeftPercent > 0 ||
                                 detectionResult.CropTopPercent > 0 ||
                                 detectionResult.CropRightPercent > 0 ||
                                 detectionResult.CropBottomPercent > 0;

            return hasValidCoords;
        }

        /// <summary>
        /// 将API返回的百分比坐标转换为PowerPoint裁剪坐标
        /// </summary>
        /// <param name="leftPercent">左边裁剪百分比</param>
        /// <param name="topPercent">上边裁剪百分比</param>
        /// <param name="rightPercent">右边裁剪百分比</param>
        /// <param name="bottomPercent">下边裁剪百分比</param>
        /// <param name="shapeWidth">PowerPoint形状宽度</param>
        /// <param name="shapeHeight">PowerPoint形状高度</param>
        /// <returns>PowerPoint裁剪坐标</returns>
        private static CropCoordinates ConvertPercentageCoordsToPptCrop(
            float leftPercent, float topPercent, float rightPercent, float bottomPercent,
            float shapeWidth, float shapeHeight)
        {
            // 直接将百分比转换为PowerPoint的裁剪points
            // API返回的百分比表示从各边裁剪的比例
            var cropCoords = new CropCoordinates
            {
                Left = leftPercent * shapeWidth,
                Top = topPercent * shapeHeight,
                Right = rightPercent * shapeWidth,
                Bottom = bottomPercent * shapeHeight
            };

            return cropCoords;
        }



        /// <summary>
        /// 直接使用百分比设置PowerPoint Shape的裁剪属性
        /// 避免任何像素到points的转换，直接基于图片的百分比位置进行裁剪
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        private static void ApplyPercentageCropToShape(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始百分比裁剪 ===");
                Debug.WriteLine($"输入百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

                // 获取图片的原始尺寸（PowerPoint中的显示尺寸）
                float shapeWidth = shape.Width;
                float shapeHeight = shape.Height;
                Debug.WriteLine($"PowerPoint形状尺寸: {shapeWidth:F2}x{shapeHeight:F2} points");

                // 直接将百分比转换为PowerPoint的裁剪points
                float cropLeft = leftPercent * shapeWidth;
                float cropTop = topPercent * shapeHeight;
                float cropRight = rightPercent * shapeWidth;
                float cropBottom = bottomPercent * shapeHeight;

                Debug.WriteLine($"计算的裁剪points: Left={cropLeft:F2}, Top={cropTop:F2}, Right={cropRight:F2}, Bottom={cropBottom:F2}");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 百分比裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"百分比裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"百分比裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 直接使用API返回的百分比坐标进行裁剪（适用于被拉伸的图片）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        private static void ApplyApiPercentageCropToShape(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始API百分比坐标裁剪 ===");
                Debug.WriteLine($"API百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

                // 重新分析：基于左边图片成功，右边图片失败的情况
                // API百分比坐标应该是边界框位置，需要转换为裁剪距离
                Debug.WriteLine("使用边界框位置转换为裁剪距离的逻辑");

                float cropLeft = leftPercent * 100;              // 从左边裁剪的百分比 = 边界框左边位置
                float cropTop = topPercent * 100;                // 从上边裁剪的百分比 = 边界框上边位置
                float cropRight = (1 - rightPercent) * 100;   // 从右边裁剪的百分比 = 100% - 边界框右边位置
                float cropBottom = (1 - bottomPercent) * 100; // 从下边裁剪的百分比 = 100% - 边界框下边位置

                // 验证保留区域大小
                float preservedWidthPercent = (rightPercent - leftPercent) * 100;
                float preservedHeightPercent = (bottomPercent - topPercent) * 100;
                Debug.WriteLine($"保留区域百分比: 宽度={preservedWidthPercent:F2}%, 高度={preservedHeightPercent:F2}%");

                Debug.WriteLine($"转换为PowerPoint裁剪百分比: Left={cropLeft:F2}%, Top={cropTop:F2}%, Right={cropRight:F2}%, Bottom={cropBottom:F2}%");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ API百分比坐标裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"API百分比坐标裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"API百分比坐标裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 使用百分比坐标进行裁剪，基于原始图片尺寸（适用于被拉伸的图片）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        /// <param name="originalPixelWidth">原始图片像素宽度</param>
        /// <param name="originalPixelHeight">原始图片像素高度</param>
        private static void ApplyPercentageCropToShapeWithOriginalSize(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent, int originalPixelWidth, int originalPixelHeight)
        {
            try
            {
                Debug.WriteLine("=== 开始基于原始尺寸的百分比裁剪 ===");
                Debug.WriteLine($"输入百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");
                Debug.WriteLine($"原始图片像素尺寸: {originalPixelWidth}x{originalPixelHeight}");

                // 根据Microsoft官方文档：裁剪是相对于图片的原始尺寸计算的
                // API百分比坐标是位置，需要转换为PowerPoint的裁剪距离
                float cropLeft = leftPercent * originalPixelWidth;  // 从左边裁剪的距离
                float cropTop = topPercent * originalPixelHeight;   // 从上边裁剪的距离
                float cropRight = (1.0f - rightPercent) * originalPixelWidth;   // 从右边裁剪的距离
                float cropBottom = (1.0f - bottomPercent) * originalPixelHeight; // 从下边裁剪的距离

                Debug.WriteLine($"基于原始尺寸计算的裁剪points: Left={cropLeft:F2}, Top={cropTop:F2}, Right={cropRight:F2}, Bottom={cropBottom:F2}");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 基于原始尺寸的百分比裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"基于原始尺寸的百分比裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"基于原始尺寸的百分比裁剪失败: {ex.Message}", ex);
            }
        }

  
        /// <summary>
        /// 直接设置PowerPoint Shape的裁剪属性
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="cropCoords">裁剪坐标</param>
        private static void ApplyCropToShape(PowerPoint.Shape shape, CropCoordinates cropCoords)
        {
            try
            {
                Debug.WriteLine("开始应用裁剪到PowerPoint形状");

                shape.PictureFormat.CropLeft = cropCoords.Left;
                shape.PictureFormat.CropTop = cropCoords.Top;
                shape.PictureFormat.CropRight = cropCoords.Right;
                shape.PictureFormat.CropBottom = cropCoords.Bottom;

                // 安全的界面刷新方式 - 优化版本，避免不必要的异常
                try
                {
                    var app = shape.Application;
                    if (app?.ActiveWindow != null)
                    {
                        // 直接使用PowerPoint支持的刷新方法，避免dynamic调用产生异常
                        try
                        {
                            // 使用GotoSlide方法强制刷新（PowerPoint原生支持）
                            var activeWindow = app.ActiveWindow;
                            if (activeWindow?.View?.Slide != null)
                            {
                                var currentSlideIndex = activeWindow.View.Slide.SlideIndex;
                                activeWindow.View.GotoSlide(currentSlideIndex);
                                Debug.WriteLine("已使用GotoSlide方法刷新PowerPoint界面");
                            }
                            else
                            {
                                Debug.WriteLine("无法获取当前幻灯片，跳过界面刷新");
                            }
                        }
                        catch (Exception refreshEx)
                        {
                            Debug.WriteLine($"PowerPoint界面刷新失败: {refreshEx.Message}");
                            // 界面刷新失败不影响核心功能，继续执行
                        }
                    }
                    else
                    {
                        Debug.WriteLine("无法获取ActiveWindow，跳过界面刷新");
                    }
                }
                catch (Exception refreshEx)
                {
                    Debug.WriteLine($"刷新界面失败: {refreshEx.Message}，继续执行");
                    // 不抛出异常，继续执行
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用裁剪到形状失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"应用裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调整图片尺寸为2.1cm高度（先旋转再缩放，保持宽高比）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        public static void AdjustImageSizeTo21cm(PowerPoint.Shape shape)
        {
            try
            {
                Debug.WriteLine("=== 开始调整图片尺寸为2.1cm高度（先旋转再缩放） ===");

                // 目标高度：2.1cm = 59.535点
                float targetHeight = 2.1f * 28.35f;

                // 获取当前图片尺寸和位置
                float originalWidth = shape.Width;
                float originalHeight = shape.Height;
                float currentLeft = shape.Left;
                float currentTop = shape.Top;
                float originalAspectRatio = originalWidth / originalHeight;

                Debug.WriteLine($"调整前图片尺寸: {originalWidth:F1}x{originalHeight:F1}, 宽高比: {originalAspectRatio:F2}");
                Debug.WriteLine($"🎯 目标高度: {targetHeight:F1}点 = {targetHeight/28.35f:F2}cm");

                // 用于计算的变量
                float calcWidth = originalWidth;
                float calcHeight = originalHeight;
                float calcAspectRatio = originalAspectRatio;
                bool wasRotated = false;

                // 1. 先判断是否需要旋转（基于当前宽高比）
                if (originalAspectRatio < 1.0f) // 瘦高图片，需要横向平铺
                {
                    Debug.WriteLine("🔄 检测到瘦高图片，先逆时针旋转90度");

                    // 逆时针旋转90度
                    shape.Rotation = shape.Rotation - 90;
                    Debug.WriteLine($"已逆时针旋转90度，当前旋转角度: {shape.Rotation}");

                    // 关键修复：PowerPoint旋转后Width和Height不会自动交换，需要手动计算
                    // 旋转90度后，视觉上的宽高应该互换
                    calcWidth = originalHeight;  // 旋转后的视觉宽度 = 原来的高度
                    calcHeight = originalWidth;  // 旋转后的视觉高度 = 原来的宽度
                    calcAspectRatio = calcWidth / calcHeight;
                    wasRotated = true;

                    Debug.WriteLine($"旋转前PowerPoint属性: {originalWidth:F1}x{originalHeight:F1}");
                    Debug.WriteLine($"旋转后视觉效果: {calcWidth:F1}x{calcHeight:F1}, 新宽高比: {calcAspectRatio:F2}");
                }
                else
                {
                    Debug.WriteLine("📍 横向图片，直接进行缩放");
                }

                // 2. 按2.1cm高度进行缩放（保持宽高比）
                float newWidth = targetHeight * calcAspectRatio;
                float newHeight = targetHeight;

                Debug.WriteLine($"缩放后尺寸: {newWidth:F1}x{newHeight:F1}点");

                // 3. 计算位置（保持中心点不变，使用原始尺寸）
                float currentCenterX = currentLeft + originalWidth / 2;
                float currentCenterY = currentTop + originalHeight / 2;

                float newLeft = currentCenterX - newWidth / 2;
                float newTop = currentCenterY - newHeight / 2;

                // 4. 获取幻灯片尺寸进行边界检查
                var slide = shape.Parent as PowerPoint.Slide;
                if (slide != null)
                {
                    var presentation = slide.Parent as PowerPoint.Presentation;
                    float slideWidth = presentation.PageSetup.SlideWidth;
                    float slideHeight = presentation.PageSetup.SlideHeight;

                    // 确保图片不超出幻灯片边界
                    if (newLeft < 10) newLeft = 10;
                    if (newTop < 10) newTop = 10;
                    if (newLeft + newWidth > slideWidth - 10) newLeft = slideWidth - newWidth - 10;
                    if (newTop + newHeight > slideHeight - 10) newTop = slideHeight - newHeight - 10;

                    Debug.WriteLine($"边界检查后位置: Left={newLeft:F2}, Top={newTop:F2}");
                }

                // 5. 应用新尺寸和位置
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                shape.Width = newWidth;
                shape.Height = newHeight;
                shape.Left = newLeft;
                shape.Top = newTop;

                // 6. 验证最终效果
                float finalAspectRatio = newWidth / newHeight;
                bool isWidthGreaterThanHeight = finalAspectRatio > 1.0f;
                bool heightIs2_1cm = Math.Abs(newHeight - targetHeight) < 0.1f;

                Debug.WriteLine($"✅ 图片尺寸调整完成: {newWidth:F1}x{newHeight:F1}点");
                Debug.WriteLine($"📐 最终宽高比: {finalAspectRatio:F2}");
                Debug.WriteLine($"🎯 高度: {newHeight:F1}点 = {newHeight/28.35f:F2}cm (目标2.1cm: {heightIs2_1cm})");
                Debug.WriteLine($"✅ 宽度>高度: {isWidthGreaterThanHeight} (宽{newWidth:F1} vs 高{newHeight:F1})");

                if (!heightIs2_1cm)
                {
                    Debug.WriteLine($"⚠️ 警告：高度偏离2.1cm目标 {Math.Abs(newHeight - targetHeight):F1}点");
                }
                if (!isWidthGreaterThanHeight)
                {
                    Debug.WriteLine($"⚠️ 警告：最终宽度不大于高度！");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"调整图片尺寸失败: {ex.Message}");
                Logger.Exception(ex);
                // 不抛出异常，避免影响主要功能
            }
        }

        /// <summary>
        /// 选中图片以展示裁剪结果，不进入裁剪模式
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        public static void SelectShapeToShowResult(PowerPoint.Shape shape)
        {
            try
            {
                try
                {
                    var app = shape.Application;
                    var activeWindow = app.ActiveWindow;
                    var selection = activeWindow.Selection;
                    selection.Unselect();
                    System.Threading.Thread.Sleep(50); // 短暂延迟确保取消选择生效
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"取消选择失败: {ex.Message}");
                }

                // 选中图片以展示裁剪结果
                shape.Select();
                Debug.WriteLine("已选中图片，展示裁剪结果");

                // 强制刷新PowerPoint界面以确保裁剪效果可见
                try
                {
                    var app = shape.Application;
                    var activeWindow = app.ActiveWindow;
                    var view = activeWindow.View;
                    view.Zoom = view.Zoom; // 触发重绘
                    Debug.WriteLine("已触发PowerPoint界面刷新");
                }
                catch (Exception refreshEx)
                {
                    Debug.WriteLine($"刷新界面失败: {refreshEx.Message}");
                }

            }
            catch (Exception ex)
            {
                Debug.WriteLine($"选中图片展示结果失败: {ex.Message}");
                Logger.Exception(ex);
                // 这个错误不是致命的，不抛出异常
            }
        }




        /// <summary>
        /// 策略3：选择最佳的裁剪策略
        /// </summary>
        private static CropValues SelectBestCropStrategy(CropValues strategy1, CropValues strategy2,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            // 如果策略2无效，直接使用策略1
            if (!strategy2.IsValid)
            {
                Debug.WriteLine("策略选择: 使用策略1(百分比)，策略2无效");
                return ValidateAndCorrectCropValues(strategy1, leftPercent, topPercent, rightPercent, bottomPercent);
            }

            // 计算两种策略的差异
            float leftDiff = Math.Abs(strategy1.Left - strategy2.Left);
            float topDiff = Math.Abs(strategy1.Top - strategy2.Top);
            float rightDiff = Math.Abs(strategy1.Right - strategy2.Right);
            float bottomDiff = Math.Abs(strategy1.Bottom - strategy2.Bottom);

            float maxDiff = Math.Max(Math.Max(leftDiff, topDiff), Math.Max(rightDiff, bottomDiff));
            Debug.WriteLine($"策略差异分析: 最大差异={maxDiff:F2} points");

            // 如果差异较小（小于5个points），优先使用百分比策略
            if (maxDiff < 5.0f)
            {
                Debug.WriteLine("策略选择: 使用策略1(百分比)，差异较小");
                return ValidateAndCorrectCropValues(strategy1, leftPercent, topPercent, rightPercent, bottomPercent);
            }

            // 如果差异较大，进行更详细的分析
            // 🔥 修正：检查百分比坐标的合理性
            // API返回的百分比坐标表示从各边裁剪的比例
            bool percentCoordsValid = leftPercent >= 0 && leftPercent <= 1 &&
                                    topPercent >= 0 && topPercent <= 1 &&
                                    rightPercent >= 0 && rightPercent <= 1 &&
                                    bottomPercent >= 0 && bottomPercent <= 1;

            // 额外检查：确保裁剪后还有内容保留
            float remainingWidthPercent = 1.0f - leftPercent - rightPercent;   // 修正：直接减去两边的裁剪比例
            float remainingHeightPercent = 1.0f - topPercent - bottomPercent;  // 修正：直接减去上下的裁剪比例
            bool hasRemainingContent = remainingWidthPercent > 0.01f && remainingHeightPercent > 0.01f;

            percentCoordsValid = percentCoordsValid && hasRemainingContent;

            Debug.WriteLine($"百分比坐标验证: 基本范围={leftPercent >= 0 && leftPercent <= 1 && topPercent >= 0 && topPercent <= 1 && rightPercent >= 0 && rightPercent <= 1 && bottomPercent >= 0 && bottomPercent <= 1}");
            Debug.WriteLine($"剩余内容检查: 宽度={remainingWidthPercent:F3}, 高度={remainingHeightPercent:F3}, 有效={hasRemainingContent}");

            if (percentCoordsValid)
            {
                Debug.WriteLine("策略选择: 使用策略1(百分比)，百分比坐标有效");
                return ValidateAndCorrectCropValues(strategy1, leftPercent, topPercent, rightPercent, bottomPercent);
            }
            else
            {
                Debug.WriteLine("策略选择: 使用策略2(像素)，百分比坐标异常");
                return ValidateAndCorrectCropValues(strategy2, leftPercent, topPercent, rightPercent, bottomPercent);
            }
        }

        /// <summary>
        /// 验证和修正裁剪值
        /// </summary>
        private static CropValues ValidateAndCorrectCropValues(CropValues cropValues,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            var correctedValues = new CropValues
            {
                Left = Math.Max(0, cropValues.Left),
                Top = Math.Max(0, cropValues.Top),
                Right = Math.Max(0, cropValues.Right),
                Bottom = Math.Max(0, cropValues.Bottom),
                Strategy = cropValues.Strategy + "(已修正)",
                IsValid = true
            };

            // 检查是否进行了修正
            bool wasCorrected = correctedValues.Left != cropValues.Left ||
                              correctedValues.Top != cropValues.Top ||
                              correctedValues.Right != cropValues.Right ||
                              correctedValues.Bottom != cropValues.Bottom;

            if (wasCorrected)
            {
                Debug.WriteLine($"⚠️ 裁剪值已修正: 原值({cropValues.Left:F2},{cropValues.Top:F2},{cropValues.Right:F2},{cropValues.Bottom:F2}) " +
                              $"-> 修正值({correctedValues.Left:F2},{correctedValues.Top:F2},{correctedValues.Right:F2},{correctedValues.Bottom:F2})");
                Logger.Warning($"裁剪坐标存在负值，已自动修正为0");
            }

            return correctedValues;
        }

        /// <summary>
        /// 应用裁剪值到形状
        /// </summary>
        private static void ApplyCropValues(PowerPoint.Shape shape, CropValues cropValues)
        {
            try
            {
                // 确保图片不锁定宽高比
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                Debug.WriteLine("✓ 已解锁图片宽高比");

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropValues.Left;
                shape.PictureFormat.CropTop = cropValues.Top;
                shape.PictureFormat.CropRight = cropValues.Right;
                shape.PictureFormat.CropBottom = cropValues.Bottom;

                Debug.WriteLine($"✓ 裁剪已应用 - 策略: {cropValues.Strategy}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用裁剪失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 验证和记录裁剪结果
        /// </summary>
        private static void VerifyAndLogCropResults(PowerPoint.Shape shape, CropValues appliedCrop,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent,
            float originalWidth, float originalHeight)
        {
            try
            {
                // 获取裁剪后的尺寸
                float finalWidth = shape.Width;
                float finalHeight = shape.Height;

                // 计算实际裁剪区域
                float actualCropWidth = originalWidth - appliedCrop.Left - appliedCrop.Right;
                float actualCropHeight = originalHeight - appliedCrop.Top - appliedCrop.Bottom;

                // 计算预期裁剪区域
                float expectedCropWidth = (rightPercent - leftPercent) * originalWidth;
                float expectedCropHeight = (bottomPercent - topPercent) * originalHeight;

                // 计算误差
                float widthError = Math.Abs(actualCropWidth - expectedCropWidth);
                float heightError = Math.Abs(actualCropHeight - expectedCropHeight);
                float errorPercentage = Math.Max(widthError / expectedCropWidth, heightError / expectedCropHeight) * 100;

                Debug.WriteLine("=== 裁剪验证结果 ===");
                Debug.WriteLine($"裁剪前尺寸: {originalWidth:F2}x{originalHeight:F2} points");
                Debug.WriteLine($"裁剪后尺寸: {finalWidth:F2}x{finalHeight:F2} points");
                Debug.WriteLine($"实际裁剪区域: {actualCropWidth:F2}x{actualCropHeight:F2} points");
                Debug.WriteLine($"预期裁剪区域: {expectedCropWidth:F2}x{expectedCropHeight:F2} points");
                Debug.WriteLine($"裁剪误差: 宽度={widthError:F2}, 高度={heightError:F2}, 误差率={errorPercentage:F1}%");

                // 如果误差超过5%，记录警告
                if (errorPercentage > 5.0f)
                {
                    Logger.Warning($"裁剪误差较大: {errorPercentage:F1}%, 可能需要调整算法");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证裁剪结果失败: {ex.Message}");
            }
        }


        /// <summary>
        /// 获取图片的原始像素尺寸
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>图片的像素宽度和高度</returns>
        private static (int width, int height) GetImagePixelDimensions(PowerPoint.Shape shape)
        {

    
                    // 获取图片的原始尺寸（以points为单位）
                    var originalWidth = shape.PictureFormat.CropLeft + shape.Width + shape.PictureFormat.CropRight;
                    var originalHeight = shape.PictureFormat.CropTop + shape.Height + shape.PictureFormat.CropBottom;

                    int pixelWidth = (int)Math.Round(originalWidth);
                    int pixelHeight = (int)Math.Round(originalHeight);

                    Debug.WriteLine($"转换为像素尺寸: {pixelWidth}x{pixelHeight}");


                        return (pixelWidth, pixelHeight);
                    
                
 
            }

        

        /// <summary>
        /// 诊断裁剪问题的综合工具
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">API检测结果</param>
        /// <returns>诊断报告</returns>
        public static string DiagnoseCropIssues(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult)
        {
            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== 裁剪问题诊断报告 ===");

                // 1. 基本信息
                report.AppendLine($"形状类型: {shape.Type}");
                report.AppendLine($"当前尺寸: {shape.Width:F2}x{shape.Height:F2} points");

                // 2. API返回的坐标信息
                report.AppendLine("\n--- API返回坐标 ---");
                report.AppendLine($"像素坐标: Left={detectionResult.CropLeft}, Top={detectionResult.CropTop}, Right={detectionResult.CropRight}, Bottom={detectionResult.CropBottom}");
                report.AppendLine($"百分比坐标: Left={detectionResult.CropLeftPercent:F6}, Top={detectionResult.CropTopPercent:F6}, Right={detectionResult.CropRightPercent:F6}, Bottom={detectionResult.CropBottomPercent:F6}");
                report.AppendLine($"图片信息: {detectionResult.ImageWidth}x{detectionResult.ImageHeight} pixels");

                // 3. 图片尺寸获取测试
                report.AppendLine("\n--- 图片尺寸获取测试 ---");
                var pixelDimensions = GetImagePixelDimensions(shape);
                int pixelWidth = pixelDimensions.width;
                int pixelHeight = pixelDimensions.height;
                report.AppendLine($"获取到的像素尺寸: {pixelWidth}x{pixelHeight}");
                report.AppendLine($"API报告的像素尺寸: {detectionResult.ImageWidth}x{detectionResult.ImageHeight}");

                bool sizeMismatch = Math.Abs(pixelWidth - detectionResult.ImageWidth) > 10 ||
                                  Math.Abs(pixelHeight - detectionResult.ImageHeight) > 10;
                if (sizeMismatch)
                {
                    report.AppendLine("⚠️ 警告: 获取的图片尺寸与API报告不一致！");
                }

                // 4. 坐标合理性检查
                report.AppendLine("\n--- 坐标合理性检查 ---");
                bool percentValid = detectionResult.CropLeftPercent >= 0 && detectionResult.CropLeftPercent <= 1 &&
                                  detectionResult.CropTopPercent >= 0 && detectionResult.CropTopPercent <= 1 &&
                                  detectionResult.CropRightPercent >= 0 && detectionResult.CropRightPercent <= 1 &&
                                  detectionResult.CropBottomPercent >= 0 && detectionResult.CropBottomPercent <= 1 &&
                                  detectionResult.CropLeftPercent < detectionResult.CropRightPercent &&
                                  detectionResult.CropTopPercent < detectionResult.CropBottomPercent;

                report.AppendLine($"百分比坐标有效性: {(percentValid ? "✓ 有效" : "✗ 无效")}");

                if (!percentValid)
                {
                    report.AppendLine("⚠️ 百分比坐标存在问题，可能导致裁剪不准确");
                }

                // 5. 当前裁剪状态
                report.AppendLine("\n--- 当前裁剪状态 ---");
                var currentCrop = $"Left={shape.PictureFormat.CropLeft:F2}, Top={shape.PictureFormat.CropTop:F2}, Right={shape.PictureFormat.CropRight:F2}, Bottom={shape.PictureFormat.CropBottom:F2}";
                report.AppendLine($"当前裁剪值: {currentCrop}");

                bool hasCropping = shape.PictureFormat.CropLeft > 0 || shape.PictureFormat.CropTop > 0 ||
                                 shape.PictureFormat.CropRight > 0 || shape.PictureFormat.CropBottom > 0;
                report.AppendLine($"是否已裁剪: {(hasCropping ? "是" : "否")}");

                // 6. 建议
                report.AppendLine("\n--- 建议 ---");
                if (sizeMismatch)
                {
                    report.AppendLine("• 图片尺寸不匹配可能是由于PowerPoint内部缩放导致，建议使用百分比坐标");
                }
                if (!percentValid)
                {
                    report.AppendLine("• 百分比坐标异常，建议检查API返回数据或使用像素坐标作为备选");
                }
                if (percentValid && sizeMismatch)
                {
                    report.AppendLine("• 推荐使用增强版裁剪方法 ApplyCropToShapeByPoint");
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                return $"诊断过程中发生错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 验证裁剪是否已应用
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>裁剪状态信息</returns>
        public static string VerifyCropApplication(PowerPoint.Shape shape)
        {
            try
            {
                if (shape.Type != Office.MsoShapeType.msoPicture)
                {
                    return "选中的形状不是图片";
                }

                var cropLeft = shape.PictureFormat.CropLeft;
                var cropTop = shape.PictureFormat.CropTop;
                var cropRight = shape.PictureFormat.CropRight;
                var cropBottom = shape.PictureFormat.CropBottom;

                var status = $"当前裁剪属性 - Left: {cropLeft:F2}, Top: {cropTop:F2}, Right: {cropRight:F2}, Bottom: {cropBottom:F2}";
                Debug.WriteLine(status);

                bool hasCropping = cropLeft > 0 || cropTop > 0 || cropRight > 0 || cropBottom > 0;
                if (hasCropping)
                {
                    return $"✓ 裁剪已应用 - {status}";
                }
                else
                {
                    return $"✗ 未检测到裁剪 - {status}";
                }
            }
            catch (Exception ex)
            {
                var error = $"验证裁剪状态失败: {ex.Message}";
                Debug.WriteLine(error);
                Logger.Exception(ex);
                return error;
            }
        }



        #region 测试方法

        /// <summary>
        /// 测试方法5：简化的EXIF旋转和直接百分比裁剪
        /// </summary>
        private static void TestCropMethod5_ScaleCorrected(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult)
        {
            try
            {
                Debug.WriteLine("--- 方式5：简化的EXIF旋转和直接百分比裁剪 ---");

                // 1. 确保标准Scale（只需要一次）
                EnsureStandardScale(shape);

                float currentWidth = shape.Width;
                float currentHeight = shape.Height;
                Debug.WriteLine($"当前形状尺寸: {currentWidth:F2}x{currentHeight:F2} points");

                // 2. 获取旋转信息
                var exifRotation = GetImageExifRotation(shape, detectionResult);
                Debug.WriteLine($"旋转检测: orientation={exifRotation.orientation}, needsRotation={exifRotation.needsRotation}");

                // 3. 获取并调整裁剪百分比
                float cropLeftPercent = detectionResult.CropLeftPercent;
                float cropTopPercent = detectionResult.CropTopPercent;
                float cropRightPercent = detectionResult.CropRightPercent;
                float cropBottomPercent = detectionResult.CropBottomPercent;

                Debug.WriteLine($"API原始裁剪百分比: L={cropLeftPercent:F3}, T={cropTopPercent:F3}, R={cropRightPercent:F3}, B={cropBottomPercent:F3}");

                // 4. 如果需要旋转，调整坐标
                if (exifRotation.needsRotation)
                {
                    var rotatedCrop = RotateCropCoordinates(cropLeftPercent, cropTopPercent, cropRightPercent, cropBottomPercent, exifRotation.orientation);
                    cropLeftPercent = rotatedCrop.left;
                    cropTopPercent = rotatedCrop.top;
                    cropRightPercent = rotatedCrop.right;
                    cropBottomPercent = rotatedCrop.bottom;
                    Debug.WriteLine($"旋转后裁剪百分比: L={cropLeftPercent:F3}, T={cropTopPercent:F3}, R={cropRightPercent:F3}, B={cropBottomPercent:F3}");
                }

                // 5. 直接基于当前尺寸计算裁剪值（简化DPI处理）
                float cropLeft = cropLeftPercent * currentWidth;
                float cropTop = cropTopPercent * currentHeight;
                float cropRight = cropRightPercent * currentWidth;
                float cropBottom = cropBottomPercent * currentHeight;

                Debug.WriteLine($"最终裁剪值: L={cropLeft:F2}, T={cropTop:F2}, R={cropRight:F2}, B={cropBottom:F2}");

                // 6. 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 裁剪已应用");

            }
            catch (Exception ex)
            {
                Debug.WriteLine($"方式5失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取图像的旋转信息（修复版：基于PowerPoint当前状态判断）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果，用于宽高比分析</param>
        /// <returns>旋转信息</returns>
        private static (int orientation, bool needsRotation) GetImageExifRotation(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult)
        {
            // 关键修复：基于PowerPoint当前状态判断，而不是检测原始图片状态
            float rotation = shape.Rotation;
            Debug.WriteLine($"🔍 PowerPoint当前旋转角度: {rotation}度");

            // 标准化旋转角度
            float normalizedRotation = rotation % 360;
            if (normalizedRotation < 0) normalizedRotation += 360;

            // 转换为EXIF Orientation值
            int orientation = ConvertRotationToExifOrientation(normalizedRotation);
            bool needsRotation = orientation != 1;

            Debug.WriteLine($"✓ 基于PowerPoint状态: {normalizedRotation}度 → EXIF Orientation {orientation}, 需要坐标调整: {needsRotation}");

            // 重要：这里的needsRotation表示需要调整裁剪坐标，而不是需要旋转图片
            return (orientation, needsRotation);
        }

        /// <summary>
        /// 从PowerPoint Shape.Rotation属性获取旋转信息（新增关键方法）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>旋转信息</returns>
        private static (int orientation, bool needsRotation) GetRotationFromPowerPointShape(PowerPoint.Shape shape)
        {
            try
            {
                float rotation = shape.Rotation;
                Debug.WriteLine($"🔄 PowerPoint Shape.Rotation: {rotation}度");

                // 将PowerPoint的旋转角度转换为EXIF Orientation值
                // PowerPoint: 正值=顺时针，负值=逆时针
                // 标准化到0-360度范围
                float normalizedRotation = rotation % 360;
                if (normalizedRotation < 0) normalizedRotation += 360;

                Debug.WriteLine($"🔄 标准化旋转角度: {normalizedRotation}度");

                // 转换为EXIF Orientation值
                int orientation = ConvertRotationToExifOrientation(normalizedRotation);
                bool needsRotation = orientation != 1;

                Debug.WriteLine($"✓ PowerPoint旋转转换: {normalizedRotation}度 → EXIF Orientation {orientation}, 需要旋转: {needsRotation}");
                return (orientation, needsRotation);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取PowerPoint旋转信息失败: {ex.Message}");
                return (1, false);
            }
        }

        /// <summary>
        /// 将旋转角度转换为EXIF Orientation值
        /// </summary>
        /// <param name="rotationDegrees">旋转角度（0-360）</param>
        /// <returns>EXIF Orientation值</returns>
        private static int ConvertRotationToExifOrientation(float rotationDegrees)
        {
            // 允许5度的误差范围
            const float tolerance = 5.0f;

            if (Math.Abs(rotationDegrees) < tolerance || Math.Abs(rotationDegrees - 360) < tolerance)
            {
                return 1; // 正常方向
            }
            else if (Math.Abs(rotationDegrees - 90) < tolerance)
            {
                return 6; // 顺时针90度
            }
            else if (Math.Abs(rotationDegrees - 180) < tolerance)
            {
                return 3; // 180度
            }
            else if (Math.Abs(rotationDegrees - 270) < tolerance)
            {
                return 8; // 逆时针90度（或顺时针270度）
            }
            else
            {
                Debug.WriteLine($"⚠️ 非标准旋转角度: {rotationDegrees}度，假设为正常方向");
                return 1; // 非标准角度，假设为正常
            }
        }

        /// <summary>
        /// 从图像数据中获取EXIF旋转信息（全面增强版）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>EXIF旋转信息</returns>
        private static (int orientation, bool needsRotation) GetImageExifRotationFromImage(PowerPoint.Shape shape)
        {
            try
            {
                Debug.WriteLine("🔍 开始全面EXIF检测");

                // 方法1：尝试从链接的原始文件获取EXIF（最可靠）
                var linkExifResult = TryGetExifFromLinkedFile(shape);
                if (linkExifResult.needsRotation)
                {
                    Debug.WriteLine("✓ 从链接原始文件获取EXIF成功");
                    return linkExifResult;
                }

                // 方法2：尝试导出为JPEG格式获取EXIF（新增）
                var jpegExifResult = TryGetExifFromJpegExport(shape);
                if (jpegExifResult.needsRotation)
                {
                    Debug.WriteLine("✓ 从JPEG导出获取EXIF成功");
                    return jpegExifResult;
                }

                // 方法3：尝试从PowerPoint提取的图像获取EXIF（原有方法，作为备用）
                var image = ImageExtractor.ExtractImageFromShape(shape);
                if (image == null)
                {
                    Debug.WriteLine("无法提取图像数据，假设无EXIF旋转");
                    return (1, false);
                }

                using (image)
                {
                    var exifResult = ReadExifFromImage(image, "PowerPoint提取图像");
                    if (exifResult.needsRotation)
                    {
                        return exifResult;
                    }
                }

                Debug.WriteLine("❌ 所有EXIF检测方法都失败，假设无旋转");
                return (1, false);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取EXIF旋转信息失败: {ex.Message}");
                return (1, false);
            }
        }

        /// <summary>
        /// 尝试通过JPEG导出获取EXIF信息（关键新方法）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>EXIF旋转信息</returns>
        private static (int orientation, bool needsRotation) TryGetExifFromJpegExport(PowerPoint.Shape shape)
        {
            string tempFilePath = null;
            try
            {
                // 创建临时JPEG文件路径
                tempFilePath = Path.Combine(Path.GetTempPath(), $"ppt_exif_check_{Guid.NewGuid()}.jpg");

                Debug.WriteLine($"🖼️ 尝试导出为JPEG获取EXIF: {tempFilePath}");

                // 导出为JPEG格式（保留EXIF信息）
                shape.Export(tempFilePath, PpShapeFormat.ppShapeFormatJPG);

                if (!File.Exists(tempFilePath))
                {
                    Debug.WriteLine("JPEG导出文件不存在");
                    return (1, false);
                }

                // 从JPEG文件读取EXIF
                using (var jpegImage = Image.FromFile(tempFilePath))
                {
                    return ReadExifFromImage(jpegImage, "JPEG导出文件");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"JPEG导出EXIF检测失败: {ex.Message}");
                return (1, false);
            }
            finally
            {
                // 清理临时文件
                if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
                {
                    try
                    {
                        File.Delete(tempFilePath);
                        Debug.WriteLine("已清理EXIF检测临时文件");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"清理临时文件失败: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 从图像对象读取EXIF信息（通用方法）
        /// </summary>
        /// <param name="image">图像对象</param>
        /// <param name="source">来源描述</param>
        /// <returns>EXIF旋转信息</returns>
        private static (int orientation, bool needsRotation) ReadExifFromImage(Image image, string source)
        {
            try
            {
                const int ExifOrientationId = 0x0112; // EXIF Orientation标签ID

                Debug.WriteLine($"📋 检查{source}的EXIF信息，PropertyItems数量: {image.PropertyItems.Length}");

                // 列出所有PropertyItems用于调试
                foreach (var prop in image.PropertyItems)
                {
                    Debug.WriteLine($"   PropertyItem ID: 0x{prop.Id:X4}, Length: {prop.Len}, Type: {prop.Type}");
                }

                var orientationProperty = image.PropertyItems.FirstOrDefault(p => p.Id == ExifOrientationId);
                if (orientationProperty != null && orientationProperty.Value.Length > 0)
                {
                    int orientation = BitConverter.ToUInt16(orientationProperty.Value, 0);
                    bool needsRotation = orientation != 1; // 1表示正常方向，其他值表示需要旋转

                    Debug.WriteLine($"✓ 从{source}检测到EXIF Orientation: {orientation}, 需要旋转: {needsRotation}");
                    return (orientation, needsRotation);
                }
                else
                {
                    Debug.WriteLine($"❌ {source}中未找到EXIF Orientation信息");
                    return (1, false);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"读取{source}EXIF失败: {ex.Message}");
                return (1, false);
            }
        }

        /// <summary>
        /// 尝试从链接的原始文件获取EXIF信息
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>EXIF旋转信息</returns>
        private static (int orientation, bool needsRotation) TryGetExifFromLinkedFile(PowerPoint.Shape shape)
        {
            try
            {
                // 检查是否为链接图片
                var linkFormat = shape.LinkFormat;
                if (linkFormat != null && !string.IsNullOrEmpty(linkFormat.SourceFullName))
                {
                    string sourcePath = linkFormat.SourceFullName;
                    Debug.WriteLine($"🔗 发现链接图片，尝试从原始文件获取EXIF: {sourcePath}");

                    if (File.Exists(sourcePath))
                    {
                        using (var originalImage = Image.FromFile(sourcePath))
                        {
                            const int ExifOrientationId = 0x0112;

                            try
                            {
                                var orientationProperty = originalImage.PropertyItems.FirstOrDefault(p => p.Id == ExifOrientationId);
                                if (orientationProperty != null && orientationProperty.Value.Length > 0)
                                {
                                    int orientation = BitConverter.ToUInt16(orientationProperty.Value, 0);
                                    bool needsRotation = orientation != 1;

                                    Debug.WriteLine($"✓ 从原始文件检测到EXIF Orientation: {orientation}, 需要旋转: {needsRotation}");
                                    return (orientation, needsRotation);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"读取原始文件EXIF失败: {ex.Message}");
                            }
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"⚠️ 链接的原始文件不存在: {sourcePath}");
                    }
                }

                return (1, false);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从链接文件获取EXIF失败: {ex.Message}");
                return (1, false);
            }
        }

        /// <summary>
        /// 通过宽高比分析判断旋转状态（智能版，尝试判断旋转方向）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <returns>旋转信息</returns>
        private static (int orientation, bool needsRotation) AnalyzeRotationByAspectRatio(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult)
        {
            try
            {
                float apiAspectRatio = (float)detectionResult.ImageWidth / detectionResult.ImageHeight;
                float shapeAspectRatio = shape.Width / shape.Height;

                Debug.WriteLine($"📊 API宽高比: {apiAspectRatio:F3} ({detectionResult.ImageWidth}x{detectionResult.ImageHeight})");
                Debug.WriteLine($"📊 Shape宽高比: {shapeAspectRatio:F3} ({shape.Width:F1}x{shape.Height:F1})");

                bool apiIsLandscape = apiAspectRatio > 1.0f;
                bool shapeIsLandscape = shapeAspectRatio > 1.0f;

                if (apiIsLandscape != shapeIsLandscape)
                {
                    Debug.WriteLine("🔄 宽高比方向不一致，需要判断旋转方向");

                    // 智能判断是90度还是270度旋转
                    var smartOrientation = DetermineRotationDirection(shape, detectionResult, apiAspectRatio, shapeAspectRatio);
                    Debug.WriteLine($"🧠 智能判断结果: orientation={smartOrientation}");
                    return (smartOrientation, true);
                }
                else
                {
                    Debug.WriteLine("✅ 宽高比方向一致，判断为未旋转");
                    return (1, false);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"宽高比分析失败: {ex.Message}");
                return (1, false);
            }
        }

        /// <summary>
        /// 智能判断旋转方向（90度 vs 270度）- 修复版
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <param name="apiAspectRatio">API图片宽高比</param>
        /// <param name="shapeAspectRatio">Shape宽高比</param>
        /// <returns>EXIF orientation值</returns>
        private static int DetermineRotationDirection(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult,
            float apiAspectRatio, float shapeAspectRatio)
        {
            try
            {
                Debug.WriteLine("🧠 开始智能判断旋转方向（修复版）");
                Debug.WriteLine($"📐 API宽高比: {apiAspectRatio:F3}, Shape宽高比: {shapeAspectRatio:F3}");

                // 方法1：基于宽高比的精确数学计算
                // 如果图片顺时针旋转90度，新的宽高比 = 1 / 原宽高比
                float expectedRatioAfter90CW = 1.0f / apiAspectRatio;  // 顺时针90度后的预期宽高比
                float expectedRatioAfter90CCW = 1.0f / apiAspectRatio; // 逆时针90度后的预期宽高比（实际相同）

                // 但是我们需要考虑的是：
                // - 如果API图片是横向的(宽>高)，旋转90度后应该变成竖向的(宽<高)
                // - 如果Shape是竖向的，说明图片被旋转了

                Debug.WriteLine($"📊 API图片方向: {(apiAspectRatio > 1 ? "横向" : "竖向")}");
                Debug.WriteLine($"📊 Shape方向: {(shapeAspectRatio > 1 ? "横向" : "竖向")}");

                // 方法2：基于裁剪区域的合理性判断（主要方法）
                var orientation6Score = EvaluateCropReasonableness(detectionResult, 6); // 顺时针90度
                var orientation8Score = EvaluateCropReasonableness(detectionResult, 8); // 逆时针90度

                Debug.WriteLine($"📊 裁剪合理性评分: 顺时针90度={orientation6Score:F2}, 逆时针90度={orientation8Score:F2}");

                // 如果评分差异明显，使用评分结果
                if (Math.Abs(orientation6Score - orientation8Score) > 0.2f)
                {
                    int bestOrientation = orientation6Score > orientation8Score ? 6 : 8;
                    Debug.WriteLine($"✓ 基于裁剪合理性选择: orientation={bestOrientation} (评分差异: {Math.Abs(orientation6Score - orientation8Score):F2})");
                    return bestOrientation;
                }

                // 方法3：基于电芯检测的常见模式
                // 大多数手机拍照的电芯图片，如果需要旋转，通常是顺时针90度
                Debug.WriteLine("📱 评分相近，使用经验规则：手机拍照通常是顺时针90度旋转");
                return 6; // 默认顺时针90度
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"智能判断旋转方向失败: {ex.Message}，默认使用顺时针90度");
                return 6; // 默认返回顺时针90度
            }
        }

        /// <summary>
        /// 评估在指定旋转方向下裁剪区域的合理性（增强版）
        /// </summary>
        /// <param name="detectionResult">检测结果</param>
        /// <param name="orientation">旋转方向</param>
        /// <returns>合理性评分（0-1，越高越合理）</returns>
        private static float EvaluateCropReasonableness(BatteryDetectionApiResult detectionResult, int orientation)
        {
            try
            {
                // 获取原始裁剪百分比
                float left = detectionResult.CropLeftPercent;
                float top = detectionResult.CropTopPercent;
                float right = detectionResult.CropRightPercent;
                float bottom = detectionResult.CropBottomPercent;

                Debug.WriteLine($"🔍 评估orientation={orientation}的合理性");
                Debug.WriteLine($"   原始坐标: L={left:F3}, T={top:F3}, R={right:F3}, B={bottom:F3}");

                // 根据旋转方向调整坐标
                var rotated = RotateCropCoordinates(left, top, right, bottom, orientation);
                Debug.WriteLine($"   旋转后坐标: L={rotated.left:F3}, T={rotated.top:F3}, R={rotated.right:F3}, B={rotated.bottom:F3}");

                // 评估合理性指标
                float score = 1.0f;
                var penalties = new List<string>();

                // 1. 检查是否有负值或超出范围的值（严重问题）
                if (rotated.left < 0 || rotated.top < 0 || rotated.right < 0 || rotated.bottom < 0)
                {
                    score -= 0.8f;
                    penalties.Add("存在负值");
                }

                if (rotated.left > 1 || rotated.top > 1 || rotated.right > 1 || rotated.bottom > 1)
                {
                    score -= 0.8f;
                    penalties.Add("超出范围");
                }

                // 2. 检查保留区域大小是否合理
                float remainingWidth = 1.0f - rotated.left - rotated.right;
                float remainingHeight = 1.0f - rotated.top - rotated.bottom;

                Debug.WriteLine($"   保留区域: 宽度={remainingWidth:F3}, 高度={remainingHeight:F3}");

                if (remainingWidth <= 0 || remainingHeight <= 0)
                {
                    score -= 0.9f; // 最严重的问题
                    penalties.Add("保留区域无效");
                }
                else if (remainingWidth < 0.1f || remainingHeight < 0.1f)
                {
                    score -= 0.5f;
                    penalties.Add("保留区域过小");
                }
                else if (remainingWidth > 0.9f || remainingHeight > 0.9f)
                {
                    score -= 0.3f;
                    penalties.Add("裁剪区域过小");
                }

                // 3. 检查裁剪比例是否合理（电芯检测通常有一定的裁剪）
                float totalCropRatio = rotated.left + rotated.right + rotated.top + rotated.bottom;
                if (totalCropRatio < 0.05f)
                {
                    score -= 0.2f;
                    penalties.Add("几乎无裁剪");
                }
                else if (totalCropRatio > 0.8f)
                {
                    score -= 0.4f;
                    penalties.Add("裁剪过多");
                }

                // 4. 检查裁剪的均衡性（电芯通常在图片中心附近）
                float leftRightBalance = Math.Abs(rotated.left - rotated.right);
                float topBottomBalance = Math.Abs(rotated.top - rotated.bottom);

                if (leftRightBalance > 0.4f)
                {
                    score -= 0.1f;
                    penalties.Add("左右不均衡");
                }
                if (topBottomBalance > 0.4f)
                {
                    score -= 0.1f;
                    penalties.Add("上下不均衡");
                }

                float finalScore = Math.Max(0, score);
                Debug.WriteLine($"   评分: {finalScore:F2}, 扣分原因: {string.Join(", ", penalties)}");

                return finalScore;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"评估裁剪合理性失败: {ex.Message}");
                return 0.1f; // 返回低评分
            }
        }

        /// <summary>
        /// 根据EXIF旋转值调整裁剪坐标
        /// </summary>
        /// <param name="left">左边裁剪百分比</param>
        /// <param name="top">顶部裁剪百分比</param>
        /// <param name="right">右边裁剪百分比</param>
        /// <param name="bottom">底部裁剪百分比</param>
        /// <param name="orientation">EXIF方向值</param>
        /// <returns>旋转后的裁剪坐标</returns>
        private static (float left, float top, float right, float bottom) RotateCropCoordinates(
            float left, float top, float right, float bottom, int orientation)
        {
            Debug.WriteLine($"旋转裁剪坐标，EXIF Orientation: {orientation}");
            Debug.WriteLine($"原始坐标: Left={left:F3}, Top={top:F3}, Right={right:F3}, Bottom={bottom:F3}");

            switch (orientation)
            {
                case 1: // 正常，无旋转
                    return (left, top, right, bottom);

                case 3: // 旋转180度
                    return (right, bottom, left, top);

                case 6: // 顺时针旋转90度（最常见的手机拍照旋转）
                    return (top, right, bottom, left);

                case 8: // 逆时针旋转90度
                    return (bottom, left, top, right);

                case 2: // 水平翻转
                    return (right, top, left, bottom);

                case 4: // 垂直翻转
                    return (left, bottom, right, top);

                case 5: // 水平翻转 + 逆时针旋转90度
                    return (bottom, right, top, left);

                case 7: // 水平翻转 + 顺时针旋转90度
                    return (top, left, bottom, right);

                default:
                    Debug.WriteLine($"未知的EXIF Orientation值: {orientation}，使用原始坐标");
                    return (left, top, right, bottom);
            }
        }

        #endregion
    }

    /// <summary>
    /// 裁剪坐标结构
    /// </summary>
    public class CropCoordinates
    {
        /// <summary>
        /// 左边裁剪距离（points）
        /// </summary>
        public float Left { get; set; }

        /// <summary>
        /// 上边裁剪距离（points）
        /// </summary>
        public float Top { get; set; }

        /// <summary>
        /// 右边裁剪距离（points）
        /// </summary>
        public float Right { get; set; }

        /// <summary>
        /// 下边裁剪距离（points）
        /// </summary>
        public float Bottom { get; set; }
    }

    /// <summary>
    /// 裁剪值结构（用于增强版裁剪方法）
    /// </summary>
    public class CropValues
    {
        /// <summary>
        /// 左边裁剪距离（points）
        /// </summary>
        public float Left { get; set; }

        /// <summary>
        /// 上边裁剪距离（points）
        /// </summary>
        public float Top { get; set; }

        /// <summary>
        /// 右边裁剪距离（points）
        /// </summary>
        public float Right { get; set; }

        /// <summary>
        /// 下边裁剪距离（points）
        /// </summary>
        public float Bottom { get; set; }

        /// <summary>
        /// 计算策略名称
        /// </summary>
        public string Strategy { get; set; }

        /// <summary>
        /// 是否为有效的裁剪值
        /// </summary>
        public bool IsValid { get; set; } = true;
    }
}
